# Tenyy 统一依赖管理文件
# 合并了crawler和download_extract两个模块的所有依赖
# 版本: 2025-07-18

# ============================================================================
# 🔄 核心框架 - Prefect工作流编排
# ============================================================================
prefect>=3.4.7
prefect-docker>=0.3.1

# ============================================================================
# 🗄️ 数据库驱动
# ============================================================================
SQLAlchemy>=2.0.0
asyncpg>=0.29.0          # 异步PostgreSQL驱动
psycopg2-binary>=2.9.0   # 同步PostgreSQL驱动

# ============================================================================
# 📦 APK分析工具
# ============================================================================
androguard==4.1.3        # APK逆向分析工具

# ============================================================================
# 📥 下载和网络请求
# ============================================================================
aria2p>=0.11.0           # Aria2 Python客户端
requests>=2.31.0         # HTTP请求库
brotli                    # Brotli压缩支持

# ============================================================================
# 🕷️ 爬虫相关依赖
# ============================================================================
beautifulsoup4            # HTML解析
lxml                      # XML/HTML解析器
pandas                    # 数据处理

# ============================================================================
# ⚙️ 配置管理
# ============================================================================
pydantic>=2.0.0          # 数据验证和设置管理
pydantic-settings>=2.0.0 # Pydantic设置扩展
python-dotenv>=1.0.0     # .env文件支持
pyyaml                    # YAML配置文件支持

# ============================================================================
# 🔧 工具库
# ============================================================================
typing-extensions>=4.7.0 # 类型提示扩展
tenacity>=8.2.0          # 重试机制
tqdm                      # 进度条
loguru>=0.7.0            # 日志库

# ============================================================================
# 🗂️ 可选依赖 - 存储和缓存 (已优化镜像体积)
# ============================================================================
# 以下依赖已移除以优化镜像体积 (减少约100-200MB)：
# - redis>=5.0.0 (当前项目未使用，约20MB)
# - rq>=2.1.0 (历史遗留，未使用，约15MB)
# - minio>=7.2.0 (当前项目未使用，约30MB)
# - pandas (爬虫可选，约100MB) - 如需要可单独安装
# - tqdm (进度条可选，约5MB) - 如需要可单独安装
