# -*- coding: utf-8 -*-
"""
Download Extract Configuration

统一配置管理模块，包含所有系统配置项。
不使用.env文件，所有配置直接在此文件中定义。
"""

import os
from typing import Optional

# ============================================================================
# 数据库配置
# ============================================================================

# PostgreSQL数据库连接配置
DATABASE_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "5434")),  # 本地开发端口
    "database": os.getenv("DB_NAME", "tenyy_app"),
    "username": os.getenv("DB_USER", "admin"),
    "password": os.getenv("DB_PASSWORD", "zhangdi168"),  # 修复：使用正确的默认密码
}

# 异步数据库连接字符串
DATABASE_URL = (
    f"postgresql+asyncpg://{DATABASE_CONFIG['username']}:"
    f"{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:"
    f"{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
)

# 同步数据库连接字符串（用于某些同步操作）
DATABASE_URL_SYNC = (
    f"postgresql+psycopg2://{DATABASE_CONFIG['username']}:"
    f"{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:"
    f"{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
)

# ============================================================================
# Aria2配置
# ============================================================================

# Aria2 RPC服务配置
ARIA2_CONFIG = {
    "rpc_url": os.getenv("ARIA2_RPC_URL", "http://localhost:6800/jsonrpc"),  # Aria2 RPC地址
    "rpc_token": os.getenv("ARIA2_RPC_TOKEN", "zhangdi168"),  # RPC认证令牌
    "download_dir": os.getenv("DOWNLOAD_DIR", "/downloads"),  # Aria2容器内下载目录（用于RPC调用）
    "local_download_dir": os.getenv("LOCAL_DOWNLOAD_DIR",
                                   "/Users/<USER>/dev/local_download/aria2-downloads" if os.getenv("TENYY_ENV") == "local"
                                   else "/downloads"),  # 本地环境使用宿主机路径，容器环境使用/downloads
    "host_download_dir": "/Users/<USER>/dev/local_download/aria2-downloads",  # 宿主机实际路径（仅用于调试）
    "max_connections": 10,  # 每个下载的最大连接数
    "split": 10,  # 分片数量，提高下载速度
    "timeout": 300,  # 下载超时时间（秒）
    "retry_wait": 5,  # 重试等待时间（秒）
    "max_retries": 3,  # 最大重试次数
}

# ============================================================================
# Prefect配置
# ============================================================================

# Prefect服务配置
PREFECT_CONFIG = {
    "api_url": "http://localhost:4200/api",  # 本地Prefect API地址
    "work_pool_name": "tenyy-unified-pool",  # 统一工作池名称
}

# ============================================================================
# 并发和性能配置
# ============================================================================

# 自适应调度器配置
SCHEDULER_CONFIG = {
    "target_concurrency": 300,  # 目标并发数
    "check_interval": 60,  # 检查间隔（秒）
    "batch_size": 10,  # 每次处理的任务批次大小
    "max_batch_size": 50,  # 最大批次大小
}

# APK处理配置
PROCESSING_CONFIG = {
    "max_concurrent_downloads": 100,  # 最大并发下载数
    "download_timeout": 600,  # 下载超时时间（秒）
    "analysis_timeout": 300,  # 分析超时时间（秒）
    "cleanup_temp_files": True,  # 是否清理临时文件
    "temp_file_prefix": "apk_temp_",  # 临时文件前缀
}

# ============================================================================
# 重试和错误处理配置
# ============================================================================

# 重试策略配置
RETRY_CONFIG = {
    "max_retries": 2,  # 最大重试次数
    "retry_delay": 60,  # 重试延迟（秒）
    "exponential_backoff": True,  # 是否使用指数退避
    "max_retry_delay": 300,  # 最大重试延迟（秒）
}

# ============================================================================
# 日志配置
# ============================================================================

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",  # 日志级别
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_path": "/tmp/download_extract.log",  # 日志文件路径
    "max_file_size": 10 * 1024 * 1024,  # 最大文件大小（10MB）
    "backup_count": 5,  # 备份文件数量
}

# ============================================================================
# 数据库表和字段配置
# ============================================================================

# 数据库表名
TABLE_NAMES = {
    "app_version": "app_version",
}

# 状态字段值定义
STATUS_VALUES = {
    "download_status": {
        "pending": "pending",
        "processing": "processing",  # 任务已被分配，正在处理中
        "downloading": "downloading",
        "downloaded": "downloaded",
        "failed": "failed",
    },
    "analyze_status": {
        "pending": "pending",
        "processing": "processing",
        "completed": "completed", 
        "failed": "failed",
    }
}

# 需要填充的分析结果字段
ANALYSIS_FIELDS = [
    "android_manifest",  # AndroidManifest.xml内容
    "packages_class",    # 包名列表（JSONB）
    "lib_files",         # so库文件列表（JSONB）
]

# ============================================================================
# 辅助函数
# ============================================================================

def get_temp_file_path(app_version_id: int, filename: Optional[str] = None) -> str:
    """
    生成临时文件路径

    Args:
        app_version_id: 应用版本ID
        filename: 可选的文件名，如果不提供则自动生成

    Returns:
        临时文件的完整路径
    """
    if filename is None:
        # 添加时间戳确保文件名唯一性，避免并发冲突
        import time
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        filename = f"{PROCESSING_CONFIG['temp_file_prefix']}{app_version_id}_{timestamp}.apk"

    temp_dir = ARIA2_CONFIG["local_download_dir"]  # 使用本地文件系统路径
    return os.path.join(temp_dir, filename)


def get_database_url(async_driver: bool = True) -> str:
    """
    获取数据库连接URL
    
    Args:
        async_driver: 是否使用异步驱动
        
    Returns:
        数据库连接字符串
    """
    return DATABASE_URL if async_driver else DATABASE_URL_SYNC


def validate_config() -> bool:
    """
    验证配置的有效性
    
    Returns:
        配置是否有效
    """
    # 检查必要的配置项
    required_configs = [
        DATABASE_CONFIG["host"],
        DATABASE_CONFIG["database"], 
        DATABASE_CONFIG["username"],
        ARIA2_CONFIG["rpc_url"],
        PREFECT_CONFIG["api_url"],
    ]
    
    return all(config for config in required_configs)


# 在模块加载时验证配置
if not validate_config():
    raise ValueError("配置验证失败，请检查必要的配置项")
