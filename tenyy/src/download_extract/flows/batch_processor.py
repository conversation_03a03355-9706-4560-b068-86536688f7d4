#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch APK Processor
批量APK处理器

使用Prefect进行批量APK下载和分析
"""

import asyncio
import sys
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 🔍 调试钩子 - 容器启动时的环境信息
print("=" * 80)
print("🔍 DEBUG: 容器启动调试信息")
print(f"📅 启动时间: {datetime.now()}")
print(f"🐳 容器ID: {os.getenv('HOSTNAME', 'unknown')}")
print(f"📦 Python路径: {sys.executable}")
print(f"📁 工作目录: {os.getcwd()}")
print(f"🔧 环境变量:")
for key in ['TENYY_ENV', 'DOCKER_ENV', 'PREFECT_API_URL', 'PYTHONPATH']:
    print(f"   {key}: {os.getenv(key, 'NOT_SET')}")
print(f"📄 当前文件: {__file__}")
print(f"📝 文件修改时间: {datetime.fromtimestamp(os.path.getmtime(__file__))}")
print("=" * 80)
import psycopg2
from prefect import flow, task, get_run_logger
from prefect.artifacts import create_markdown_artifact
# from prefect.task_runners import ConcurrentTaskRunner  # 暂时不使用
from prefect.client.schemas import FlowRun

# 添加路径
sys.path.append('.')
sys.path.append('../..')

from tenyy.src.download_extract.config import DATABASE_CONFIG, PROCESSING_CONFIG
from tenyy.src.download_extract.flows.apk_processor import process_single_apk, DatabaseManager


@task(name="清理超时的processing状态", retries=1)
async def cleanup_stale_processing_tasks(timeout_minutes: int = 30) -> int:
    """清理超时的processing状态任务，避免任务永久卡住"""
    logger = get_run_logger()

    try:
        conn = psycopg2.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            database=DATABASE_CONFIG['database'],
            user=DATABASE_CONFIG['username'],
            password=DATABASE_CONFIG['password']
        )
        cur = conn.cursor()

        # 清理超过指定时间的processing状态任务
        cleanup_query = f"""
            UPDATE app_version
            SET download_status = NULL,
                last_updated = NOW()
            WHERE download_status = 'processing'
            AND last_updated < NOW() - INTERVAL '{timeout_minutes} minutes'
        """

        cur.execute(cleanup_query)
        cleaned_count = cur.rowcount
        conn.commit()
        conn.close()

        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个超时的processing状态任务")

        return cleaned_count

    except Exception as e:
        logger.error(f"清理processing状态任务失败: {e}")
        return 0


@task(name="Create Download Analysis Artifact")
def create_download_analysis_artifact(summary: Dict[str, Any], duration: float,
                                     flow_type: str = "batch") -> None:
    """
    创建下载分析模块的Artifact报告

    Args:
        summary: 处理结果摘要
        duration: 执行时长（秒）
        flow_type: 流程类型 ("batch" 或 "full")
    """

    # 计算统计信息
    success_rate = summary.get('success_rate', 0)
    total_tasks = summary.get('total_tasks', 0)
    successful_tasks = summary.get('successful_tasks', 0)
    failed_tasks = summary.get('failed_tasks', 0)

    # 下载数据统计
    download_successful = summary.get('download_successful', 0)
    download_failed = summary.get('download_failed', 0)
    total_download_size = summary.get('total_download_size', 0)
    avg_download_size = summary.get('avg_download_size', 0)
    download_speed = summary.get('avg_download_speed', 0)

    # 分析数据统计
    total_packages = summary.get('total_packages_extracted', 0)
    total_lib_files = summary.get('total_lib_files_extracted', 0)
    avg_packages = summary.get('avg_packages_per_apk', 0)
    avg_lib_files = summary.get('avg_lib_files_per_apk', 0)

    # 生成状态图标
    status_icon = "🎉" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"

    # 生成Markdown报告
    markdown_content = f"""# {status_icon} APK下载分析报告

## 📊 执行概览

| 指标 | 数值 |
|------|------|
| **流程类型** | {flow_type.upper()} |
| **执行时间** | {duration:.2f} 秒 ({duration/60:.1f} 分钟) |
| **总任务数** | {total_tasks} |
| **成功任务** | {successful_tasks} |
| **失败任务** | {failed_tasks} |
| **成功率** | {success_rate:.2f}% |

## 📥 下载统计详情

| 下载指标 | 数值 | 说明 |
|----------|------|------|
| **下载成功** | {download_successful} 个 | 成功下载的APK文件数 |
| **下载失败** | {download_failed} 个 | 下载失败的APK文件数 |
| **总下载量** | {total_download_size/1024/1024:.1f} MB | 累计下载文件大小 |
| **平均文件大小** | {avg_download_size/1024/1024:.1f} MB | 单个APK平均大小 |
| **平均下载速度** | {download_speed:.1f} KB/s | 网络下载平均速度 |

## 🔍 分析结果统计

| 分析项目 | 总数 | 平均每APK |
|----------|------|-----------|
| **提取包名** | {total_packages:,} | {avg_packages:.1f} |
| **提取库文件** | {total_lib_files:,} | {avg_lib_files:.1f} |
| **AndroidManifest** | {successful_tasks} 个 | - |

## 📈 性能指标

- **平均处理速度**: {total_tasks/duration*60:.1f} 任务/分钟
- **数据提取效率**: {(total_packages + total_lib_files)/duration:.1f} 项/秒
- **成功任务占比**: {successful_tasks/total_tasks*100:.1f}% (目标: >90%)

## 🎯 质量评估

"""

    # 添加质量评估
    if success_rate >= 90:
        markdown_content += "✅ **优秀** - 处理质量达到预期标准\n"
    elif success_rate >= 70:
        markdown_content += "⚠️ **良好** - 处理质量可接受，建议优化失败任务\n"
    else:
        markdown_content += "❌ **需改进** - 处理质量较低，需要排查问题\n"

    # 添加失败任务信息
    failed_task_ids = summary.get('failed_task_ids', [])
    if failed_task_ids:
        markdown_content += f"\n## ❌ 失败任务详情\n\n"
        markdown_content += f"失败任务数量: {len(failed_task_ids)}\n\n"
        if len(failed_task_ids) <= 10:
            markdown_content += f"失败任务ID: {', '.join(map(str, failed_task_ids))}\n"
        else:
            markdown_content += f"失败任务ID (前10个): {', '.join(map(str, failed_task_ids[:10]))}\n"
            markdown_content += f"... 还有 {len(failed_task_ids) - 10} 个失败任务\n"

    # 添加时间戳
    markdown_content += f"\n---\n**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    markdown_content += f"**流程类型**: APK下载分析 ({flow_type})\n"

    # 创建artifact
    artifact_key = f"download-analysis-{flow_type}-{int(time.time())}"

    try:
        # 使用与爬虫模块相同的方式创建artifact
        artifact_result = create_markdown_artifact(
            key=artifact_key,
            markdown=markdown_content,
            description=f"APK下载分析报告 ({flow_type})"
        )
        print(f"✅ Artifact创建成功: {artifact_key}")

        # 记录到日志
        try:
            logger = get_run_logger()
            logger.info(f"Created download analysis artifact: {artifact_result}")
        except:
            pass

    except Exception as e:
        # 如果artifact创建失败，记录日志但不影响主流程
        try:
            logger = get_run_logger()
            logger.warning(f"创建artifact失败: {e}")
        except:
            pass
        print(f"Warning: Failed to create artifact: {e}")

@task(name="获取待处理任务", retries=3)
async def get_pending_tasks(batch_size: int = 50) -> List[Dict[str, Any]]:
    """获取待处理的APK任务"""
    logger = get_run_logger()
    
    try:
        conn = psycopg2.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            database=DATABASE_CONFIG['database'],
            user=DATABASE_CONFIG['username'],
            password=DATABASE_CONFIG['password']
        )
        cur = conn.cursor()
        
        # 使用原子操作获取并锁定待处理任务
        # 使用UPDATE...RETURNING来原子性地标记和获取任务
        query = f"""
            WITH tasks_to_process AS (
                SELECT id
                FROM app_version
                WHERE download_status IS NULL
                AND download_url IS NOT NULL
                AND download_url <> ''
                LIMIT {batch_size}
                FOR UPDATE SKIP LOCKED
            )
            UPDATE app_version
            SET download_status = 'processing',
                last_updated = NOW()
            WHERE id IN (SELECT id FROM tasks_to_process)
            RETURNING id, download_url, store_app_id, version, apk_hash;
        """
        cur.execute(query)

        results = cur.fetchall()
        
        tasks = []
        for row in results:
            try:
                if len(row) >= 5:
                    task_id, download_url, store_app_id, version, apk_hash = row[:5]
                    tasks.append({
                        "id": task_id,
                        "download_url": download_url,
                        "store_app_id": store_app_id,
                        "version": version,
                        "apk_hash": apk_hash
                    })
                else:
                    logger.warning(f"跳过不完整的记录: {row}")
            except Exception as e:
                logger.warning(f"解析记录失败: {row}, 错误: {e}")
        
        conn.close()
        
        logger.info(f"获取到 {len(tasks)} 个待处理任务")
        return tasks
        
    except Exception as e:
        logger.error(f"获取待处理任务失败: {e}")
        raise

@task(name="处理单个APK", retries=2)
async def process_apk_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理单个APK任务"""
    logger = get_run_logger()
    
    task_id = task_data["id"]
    logger.info(f"开始处理任务 {task_id}")
    
    try:
        # 调用现有的APK处理函数
        result = await process_single_apk(task_data)
        
        logger.info(f"任务 {task_id} 处理完成: {result.get('status', 'unknown')}")
        
        return {
            "task_id": task_id,
            "app_version_id": task_data.get("id"),  # 添加app_version_id用于调试
            "status": result.get("status", "failed"),
            "message": result.get("message", ""),
            "analysis_summary": result.get("analysis_summary", {}),
            "download_info": result.get("download_info", {}),  # 添加下载信息
            "error": result.get("error", None)
        }
        
    except Exception as e:
        logger.error(f"任务 {task_id} 处理异常: {e}")
        return {
            "task_id": task_id,
            "status": "failed",
            "message": f"处理异常: {str(e)}",
            "analysis_summary": {},
            "error": str(e)
        }

@task(name="统计处理结果")
async def summarize_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """统计处理结果"""
    logger = get_run_logger()
    
    total_tasks = len(results)
    successful_tasks = [r for r in results if r["status"] == "success"]
    failed_tasks = [r for r in results if r["status"] == "failed"]
    
    # 统计下载数据
    total_download_size = 0
    total_download_time = 0
    download_successful = 0
    download_failed = 0

    # 统计分析数据
    total_packages = 0
    total_lib_files = 0
    total_manifest_size = 0

    for result in results:
        # 下载统计
        if result["status"] == "success":
            download_successful += 1
            # 从结果中获取下载信息
            download_info = result.get("download_info", {})
            file_size = download_info.get("file_size", 0)
            download_time = download_info.get("download_time", 0)



            total_download_size += file_size
            total_download_time += download_time
        else:
            download_failed += 1

        # 分析统计 (只统计成功的)
        if result["status"] == "success":
            summary = result.get("analysis_summary", {})
            total_packages += summary.get("packages_count", 0)
            total_lib_files += summary.get("lib_files_count", 0)
            total_manifest_size += summary.get("manifest_size", 0)
    
    # 计算下载相关的平均值
    avg_download_size = total_download_size / download_successful if download_successful > 0 else 0
    avg_download_speed = total_download_size / total_download_time if total_download_time > 0 else 0

    summary = {
        "total_tasks": total_tasks,
        "successful_tasks": len(successful_tasks),
        "failed_tasks": len(failed_tasks),
        "success_rate": len(successful_tasks) / total_tasks * 100 if total_tasks > 0 else 0,
        # 下载统计
        "download_successful": download_successful,
        "download_failed": download_failed,
        "total_download_size": total_download_size,
        "avg_download_size": avg_download_size,
        "avg_download_speed": avg_download_speed / 1024,  # 转换为KB/s
        # 分析统计
        "total_packages_extracted": total_packages,
        "total_lib_files_extracted": total_lib_files,
        "total_manifest_size": total_manifest_size,
        "avg_packages_per_apk": total_packages / len(successful_tasks) if successful_tasks else 0,
        "avg_lib_files_per_apk": total_lib_files / len(successful_tasks) if successful_tasks else 0,
        "failed_task_ids": [r["task_id"] for r in failed_tasks]
    }
    
    logger.info(f"批量处理完成统计:")
    logger.info(f"  总任务数: {summary['total_tasks']}")
    logger.info(f"  成功任务: {summary['successful_tasks']}")
    logger.info(f"  失败任务: {summary['failed_tasks']}")
    logger.info(f"  成功率: {summary['success_rate']:.2f}%")
    logger.info(f"  提取包名总数: {summary['total_packages_extracted']}")
    logger.info(f"  提取库文件总数: {summary['total_lib_files_extracted']}")
    
    return summary

@flow(
    name="批量APK处理流程",
    description="批量下载和分析APK文件",
    log_prints=True
)
async def batch_apk_processing_flow(
    batch_size: int = 50,
    max_concurrent: int = 10
) -> Dict[str, Any]:
    """批量APK处理主流程"""
    logger = get_run_logger()
    
    logger.info(f"🚀 开始批量APK处理")
    logger.info(f"批量大小: {batch_size}, 最大并发: {max_concurrent}")

    start_time = datetime.now()

    # 0. 清理超时的processing状态任务
    logger.info("0️⃣ 清理超时的processing状态任务...")
    cleaned_count = await cleanup_stale_processing_tasks(timeout_minutes=30)
    if cleaned_count > 0:
        logger.info(f"清理了 {cleaned_count} 个超时任务")

    # 1. 获取待处理任务（现在会原子性地标记为processing）
    logger.info("1️⃣ 获取并锁定待处理任务...")
    tasks = await get_pending_tasks(batch_size)
    
    if not tasks:
        logger.info("没有待处理的任务")
        return {
            "status": "completed",
            "message": "没有待处理的任务",
            "summary": {
                "total_tasks": 0,
                "successful_tasks": 0,
                "failed_tasks": 0,
                "success_rate": 0
            }
        }
    
    logger.info(f"获取到 {len(tasks)} 个任务")
    
    # 2. 流水线并发处理APK任务
    logger.info("2️⃣ 开始流水线并发处理APK任务...")
    logger.info(f"🔄 使用信号量控制并发数: {max_concurrent}")
    logger.info(f"⚡ 流水线模式: 任务完成立即开始下一个，无批次等待")

    # 使用信号量控制并发数，实现真正的流水线处理
    semaphore = asyncio.Semaphore(max_concurrent)

    async def worker(task):
        """工作协程：使用信号量控制并发"""
        async with semaphore:
            return await process_apk_task(task)

    # 所有任务同时启动，但并发数受信号量控制
    # 这样一个任务完成立即开始下一个，无需等待整个批次
    logger.info(f"🚀 启动 {len(tasks)} 个任务的流水线处理...")
    all_results = await asyncio.gather(
        *[worker(task) for task in tasks],
        return_exceptions=True
    )

    # 处理异常结果
    processed_results = []
    for i, result in enumerate(all_results):
        if isinstance(result, Exception):
            logger.error(f"任务 {tasks[i]['id']} 处理异常: {result}")
            processed_results.append({
                "task_id": tasks[i]["id"],
                "status": "failed",
                "message": f"处理异常: {str(result)}",
                "analysis_summary": {},
                "error": str(result)
            })
        else:
            processed_results.append(result)

    all_results = processed_results
    
    # 3. 统计结果
    logger.info("3️⃣ 统计处理结果...")
    summary = await summarize_results(all_results)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 4. 生成Artifact报告
    logger.info("4️⃣ 生成Artifact报告...")
    try:
        create_download_analysis_artifact(
            summary=summary,
            duration=duration.total_seconds(),
            flow_type="batch"
        )
        logger.info("✅ Artifact报告生成成功")
    except Exception as e:
        logger.warning(f"Artifact报告生成失败: {e}")

    # 5. 最终报告
    logger.info("5️⃣ 生成最终报告...")

    final_report = {
        "status": "completed",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "duration_seconds": duration.total_seconds(),
        "batch_size": batch_size,
        "max_concurrent": max_concurrent,
        "summary": summary,
        "detailed_results": all_results
    }
    
    logger.info("=" * 70)
    logger.info("🎉 批量APK处理完成!")
    logger.info("=" * 70)
    logger.info(f"处理时间: {duration}")
    logger.info(f"总任务数: {summary['total_tasks']}")
    logger.info(f"成功任务: {summary['successful_tasks']}")
    logger.info(f"失败任务: {summary['failed_tasks']}")
    logger.info(f"成功率: {summary['success_rate']:.2f}%")
    logger.info(f"平均每个APK包名: {summary['avg_packages_per_apk']:.1f}")
    logger.info(f"平均每个APK库文件: {summary['avg_lib_files_per_apk']:.1f}")
    
    if summary['failed_tasks'] > 0:
        logger.warning(f"失败任务ID: {summary['failed_task_ids']}")
    
    return final_report

@flow(
    name="全量APK处理流程",
    description="处理数据库中所有待处理的APK",
    log_prints=True
)
async def full_batch_processing_flow(
    batch_size: int = 20,
    max_concurrent: int = 3  # 降低并发数，避免系统瘫痪
) -> Dict[str, Any]:
    """全量APK处理流程"""
    logger = get_run_logger()
    
    logger.info("🚀 开始全量APK处理")
    logger.info(f"批量大小: {batch_size}, 最大并发: {max_concurrent}")
    
    start_time = datetime.now()
    all_summaries = []
    total_processed = 0
    
    # 循环处理直到没有更多任务
    batch_number = 1
    
    while True:
        logger.info(f"=" * 50)
        logger.info(f"🔄 开始第 {batch_number} 批次处理")
        logger.info(f"=" * 50)
        
        # 执行批量处理
        batch_result = await batch_apk_processing_flow(
            batch_size=batch_size,
            max_concurrent=max_concurrent
        )
        
        summary = batch_result["summary"]
        
        if summary["total_tasks"] == 0:
            logger.info("✅ 所有任务处理完成，没有更多待处理任务")
            break
        
        all_summaries.append(summary)
        total_processed += summary["total_tasks"]
        
        logger.info(f"第 {batch_number} 批次完成:")
        logger.info(f"  处理任务: {summary['total_tasks']}")
        logger.info(f"  成功: {summary['successful_tasks']}")
        logger.info(f"  失败: {summary['failed_tasks']}")
        logger.info(f"  累计处理: {total_processed}")
        
        batch_number += 1
        
        # 批次间休息
        logger.info("批次间休息5秒...")
        await asyncio.sleep(5)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 汇总所有批次的统计
    total_summary = {
        "total_batches": len(all_summaries),
        "total_tasks": sum(s["total_tasks"] for s in all_summaries),
        "total_successful": sum(s["successful_tasks"] for s in all_summaries),
        "total_failed": sum(s["failed_tasks"] for s in all_summaries),
        "total_packages_extracted": sum(s["total_packages_extracted"] for s in all_summaries),
        "total_lib_files_extracted": sum(s["total_lib_files_extracted"] for s in all_summaries),
        "success_rate": 0.0,
        "avg_packages_per_apk": 0.0,
        "avg_lib_files_per_apk": 0.0,
        "failed_task_ids": []
    }

    if total_summary["total_tasks"] > 0:
        total_summary["success_rate"] = (
            total_summary["total_successful"] / total_summary["total_tasks"] * 100
        )

    if total_summary["total_successful"] > 0:
        total_summary["avg_packages_per_apk"] = (
            total_summary["total_packages_extracted"] / total_summary["total_successful"]
        )
        total_summary["avg_lib_files_per_apk"] = (
            total_summary["total_lib_files_extracted"] / total_summary["total_successful"]
        )

    # 收集所有失败任务ID
    for summary in all_summaries:
        total_summary["failed_task_ids"].extend(summary.get("failed_task_ids", []))

    # 生成全量处理的Artifact报告
    logger.info("📊 生成全量处理Artifact报告...")
    try:
        create_download_analysis_artifact(
            summary=total_summary,
            duration=duration.total_seconds(),
            flow_type="full"
        )
        logger.info("✅ 全量处理Artifact报告生成成功")
    except Exception as e:
        logger.warning(f"全量处理Artifact报告生成失败: {e}")
    
    final_report = {
        "status": "completed",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "duration_seconds": duration.total_seconds(),
        "total_summary": total_summary,
        "batch_summaries": all_summaries
    }
    
    # 最终报告
    logger.info("=" * 80)
    logger.info("🎉 全量APK处理完成!")
    logger.info("=" * 80)
    logger.info(f"总处理时间: {duration}")
    logger.info(f"处理批次数: {total_summary['total_batches']}")
    logger.info(f"总任务数: {total_summary['total_tasks']}")
    logger.info(f"成功任务: {total_summary['total_successful']}")
    logger.info(f"失败任务: {total_summary['total_failed']}")
    logger.info(f"整体成功率: {total_summary['success_rate']:.2f}%")
    logger.info(f"提取包名总数: {total_summary['total_packages_extracted']}")
    logger.info(f"提取库文件总数: {total_summary['total_lib_files_extracted']}")
    
    if total_summary["total_tasks"] > 0:
        avg_time_per_task = duration.total_seconds() / total_summary["total_tasks"]
        logger.info(f"平均每任务耗时: {avg_time_per_task:.2f} 秒")
    
    return final_report

if __name__ == "__main__":
    # 直接运行全量处理
    import asyncio
    
    async def main():
        result = await full_batch_processing_flow(
            batch_size=20,  # 每批20个任务
            max_concurrent=3  # 最大3个并发（避免系统瘫痪）
        )
        print("处理完成:", result["status"])
        print("总任务数:", result["total_summary"]["total_tasks"])
        print("成功率:", f"{result['total_summary']['success_rate']:.2f}%")
    
    asyncio.run(main())


# ============================================================================
# 🚀 真正的流水线处理Flow - 无批次等待
# ============================================================================

@flow(
    name="真正的流水线APK处理流程",
    description="使用任务队列+工作池实现真正的流水线处理，无批次等待",
    log_prints=True
)
async def continuous_pipeline_apk_processing_flow(
    max_concurrent: int = 10,
    task_fetch_size: int = 20,
    max_idle_time: int = 60,
    check_interval: int = 5
) -> Dict[str, Any]:
    """
    真正的流水线APK处理流程 - 无批次等待，连续处理

    🚀 核心特点：
    1. 任务生产者：持续从数据库获取任务放入队列
    2. 任务消费者池：始终保持max_concurrent个工作协程运行
    3. 无批次概念：任务完成立即开始下一个
    4. 动态负载：队列空时自动获取新任务

    📊 参数说明：
    - max_concurrent: 并发工作者数量
      * 建议值: 5-20，根据服务器性能调整
      * 太少: 资源浪费，处理慢
      * 太多: 可能超载，影响稳定性

    - task_fetch_size: 每次从数据库获取的任务数
      * 建议值: 10-50，平衡数据库效率和内存使用
      * 太少: 数据库查询频繁
      * 太多: 内存占用大，不够灵活

    - max_idle_time: 空闲多久后停止（秒）
      * 建议值: 30-300，根据任务产生频率调整
      * 太短: 可能过早停止
      * 太长: 资源占用时间长

    - check_interval: 检查新任务的间隔（秒）
      * 建议值: 3-10，平衡响应速度和系统负载
      * 太短: 数据库压力大
      * 太长: 发现新任务延迟
    """
    logger = get_run_logger()

    logger.info("🚀 启动真正的流水线APK处理流程")
    logger.info(f"⚙️ 配置: 最大并发={max_concurrent}, 获取批次={task_fetch_size}")
    logger.info(f"⏰ 空闲超时={max_idle_time}秒, 检查间隔={check_interval}秒")

    # 任务队列和统计
    task_queue = asyncio.Queue()
    results = []
    stats = {
        "total_processed": 0,
        "successful": 0,
        "failed": 0,
        "start_time": datetime.now()
    }

    # 控制标志
    producer_running = True
    last_task_time = datetime.now()

    async def task_producer():
        """任务生产者：持续从数据库获取任务"""
        nonlocal producer_running, last_task_time

        while producer_running:
            try:
                # 获取新任务
                new_tasks = await get_pending_tasks(task_fetch_size)

                if new_tasks:
                    logger.info(f"📥 获取到 {len(new_tasks)} 个新任务，加入队列")
                    for task in new_tasks:
                        await task_queue.put(task)
                    last_task_time = datetime.now()
                else:
                    # 没有新任务，检查是否应该停止
                    idle_time = (datetime.now() - last_task_time).seconds
                    if idle_time > max_idle_time:
                        logger.info(f"⏹️ 空闲时间超过 {max_idle_time} 秒，停止任务生产者")
                        producer_running = False
                        break

                    logger.info(f"😴 暂无新任务，等待 {check_interval} 秒后重试...")

                # 等待一段时间再检查
                await asyncio.sleep(check_interval)

            except Exception as e:
                logger.error(f"❌ 任务生产者异常: {e}")
                await asyncio.sleep(check_interval)

    async def task_worker(worker_id: int):
        """任务工作者：从队列获取任务并处理"""
        nonlocal stats

        logger.info(f"👷 工作者 {worker_id} 启动")

        while True:
            try:
                # 从队列获取任务，设置超时避免无限等待
                try:
                    task = await asyncio.wait_for(task_queue.get(), timeout=check_interval * 2)
                except asyncio.TimeoutError:
                    # 检查是否应该停止
                    if not producer_running and task_queue.empty():
                        logger.info(f"👷 工作者 {worker_id} 完成工作，退出")
                        break
                    continue

                # 处理任务
                task_id = task['id']
                logger.info(f"👷 工作者 {worker_id} 开始处理任务 {task_id}")
                result = await process_apk_task(task)

                # 统计结果
                stats["total_processed"] += 1
                if result.get("status") == "success":
                    stats["successful"] += 1
                else:
                    stats["failed"] += 1
                results.append(result)

                # 标记任务完成
                # 标记任务完成
                task_queue.task_done()

                logger.info(f"👍 工作者 {worker_id} 完成任务 {task_id} 的处理周期")

            except Exception as e:
                logger.error(f"❌ 工作者 {worker_id} 处理任务异常: {e}")
                stats["failed"] += 1
                task_queue.task_done()

    # 启动任务生产者
    producer_task = asyncio.create_task(task_producer())

    # 启动工作者池
    worker_tasks = [
        asyncio.create_task(task_worker(i))
        for i in range(max_concurrent)
    ]

    logger.info(f"🏭 启动 1 个任务生产者 + {max_concurrent} 个工作者")

    # 等待所有任务完成
    try:
        # 等待生产者完成
        await producer_task

        # 等待队列中的所有任务处理完成
        await task_queue.join()

        # 停止所有工作者
        for task in worker_tasks:
            task.cancel()

        # 等待工作者清理
        await asyncio.gather(*worker_tasks, return_exceptions=True)

    except Exception as e:
        logger.error(f"❌ 流水线处理异常: {e}")
        # 清理资源
        producer_task.cancel()
        for task in worker_tasks:
            task.cancel()
        raise

    # 统计结果
    end_time = datetime.now()
    duration = end_time - stats["start_time"]
    success_rate = stats["successful"] / stats["total_processed"] if stats["total_processed"] > 0 else 0

    summary = {
        "total_tasks": stats["total_processed"],
        "successful_tasks": stats["successful"],
        "failed_tasks": stats["failed"],
        "success_rate": success_rate,
        "duration_seconds": duration.total_seconds(),
        "tasks_per_minute": stats["total_processed"] / (duration.total_seconds() / 60) if duration.total_seconds() > 0 else 0
    }

    logger.info("🎉 流水线处理完成")
    logger.info(f"📊 总任务: {summary['total_tasks']}")
    logger.info(f"✅ 成功: {summary['successful_tasks']}")
    logger.info(f"❌ 失败: {summary['failed_tasks']}")
    logger.info(f"📈 成功率: {summary['success_rate']:.2%}")
    logger.info(f"⏱️ 耗时: {summary['duration_seconds']:.1f} 秒")
    logger.info(f"🚀 处理速度: {summary['tasks_per_minute']:.1f} 任务/分钟")

    return {
        "status": "completed",
        "message": f"流水线处理完成，共处理 {summary['total_tasks']} 个任务",
        "summary": summary,
        "results": results
    }


@task(name="Create Continuous Processing Artifact")
async def create_continuous_processing_artifact(
    cycle_count: int,
    total_processed: int,
    total_successful: int,
    total_failed: int,
    success_rate: float,
    total_duration: float,
    batch_size: int,
    max_concurrent: int
):
    """生成循环批量处理报告"""

    # 格式化时间
    hours = int(total_duration // 3600)
    minutes = int((total_duration % 3600) // 60)
    seconds = int(total_duration % 60)
    duration_str = f"{hours}小时{minutes}分钟{seconds}秒" if hours > 0 else f"{minutes}分钟{seconds}秒"

    # 计算处理速度
    tasks_per_hour = (total_processed / total_duration * 3600) if total_duration > 0 else 0

    markdown_content = f"""
# 🔄 循环批量APK处理报告

## 📊 总体统计

| 指标 | 数值 | 说明 |
|------|------|------|
| **循环次数** | {cycle_count} 轮 | 完成的处理轮次 |
| **总处理任务** | {total_processed} 个 | 累计处理的APK任务数 |
| **成功任务** | {total_successful} 个 | 成功下载和分析的任务 |
| **失败任务** | {total_failed} 个 | 处理失败的任务 |
| **成功率** | {success_rate:.1f}% | 任务处理成功率 |
| **总耗时** | {duration_str} | 从开始到结束的总时间 |
| **处理速度** | {tasks_per_hour:.1f} 任务/小时 | 平均处理速度 |

## ⚙️ 处理配置

| 配置项 | 数值 | 说明 |
|--------|------|------|
| **批量大小** | {batch_size} | 每批处理的任务数量 |
| **最大并发** | {max_concurrent} | 同时处理的最大任务数 |

## 🎯 处理效果

{'✅ **处理完成**：所有待处理任务已完成' if total_processed > 0 else '⚠️ **无任务处理**：没有找到待处理的任务'}

## 📈 性能分析

- **平均每轮处理**: {total_processed/cycle_count:.1f} 个任务 (如果有任务)
- **平均每轮耗时**: {total_duration/cycle_count:.1f} 秒
- **系统吞吐量**: {tasks_per_hour:.1f} 任务/小时

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

    # 生成唯一的artifact key
    timestamp = int(time.time())
    artifact_key = f"continuous-apk-processing-{timestamp}"

    create_markdown_artifact(
        key=artifact_key,
        markdown=markdown_content,
        description=f"循环批量APK处理报告 - {cycle_count}轮处理"
    )


@flow(
    name="循环批量APK处理流程",
    description="持续循环批量下载和分析APK文件，直到没有待处理任务",
    log_prints=True
)
async def continuous_batch_apk_processing_flow(
    batch_size: int = 50,
    max_concurrent: int = 10,
    max_cycles: int = 100,  # 最大循环次数，防止无限循环
    cycle_delay: int = 30   # 每个循环间隔秒数
) -> Dict[str, Any]:
    """
    持续循环批量APK处理流程

    Args:
        batch_size: 每批处理的任务数量
        max_concurrent: 最大并发数
        max_cycles: 最大循环次数
        cycle_delay: 循环间隔时间(秒)

    Returns:
        处理结果统计
    """
    logger = get_run_logger()

    # 🔍 调试钩子 - 函数执行时的详细信息
    print("🚀 DEBUG: continuous_batch_apk_processing_flow 函数开始执行")
    print(f"📅 执行时间: {datetime.now()}")
    print(f"🐳 容器ID: {os.getenv('HOSTNAME', 'unknown')}")
    print(f"📦 Python版本: {sys.version}")
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"📄 函数文件: {__file__}")
    print(f"🔧 函数参数: batch_size={batch_size}, max_concurrent={max_concurrent}, max_cycles={max_cycles}, cycle_delay={cycle_delay}")
    logger.info("🔍 DEBUG: continuous_batch_apk_processing_flow 函数成功启动")

    logger.info(f"🔄 开始持续循环批量APK处理")
    logger.info(f"批量大小: {batch_size}, 最大并发: {max_concurrent}")
    logger.info(f"最大循环次数: {max_cycles}, 循环间隔: {cycle_delay}秒")

    overall_start_time = datetime.now()
    cycle_count = 0
    total_processed = 0
    total_successful = 0
    total_failed = 0

    while cycle_count < max_cycles:
        cycle_count += 1
        logger.info(f"🔄 开始第 {cycle_count} 轮循环处理")

        # 执行单次批量处理
        batch_result = await batch_apk_processing_flow(
            batch_size=batch_size,
            max_concurrent=max_concurrent
        )

        # 统计结果
        summary = batch_result.get("summary", {})
        batch_total = summary.get("total_tasks", 0)
        batch_successful = summary.get("successful_tasks", 0)
        batch_failed = summary.get("failed_tasks", 0)

        total_processed += batch_total
        total_successful += batch_successful
        total_failed += batch_failed

        logger.info(f"第 {cycle_count} 轮完成: 处理 {batch_total} 个任务, 成功 {batch_successful} 个, 失败 {batch_failed} 个")

        # 如果没有任务了，结束循环
        if batch_total == 0:
            logger.info("✅ 没有更多待处理任务，循环结束")
            break

        # 如果还有更多循环，等待一段时间
        if cycle_count < max_cycles:
            logger.info(f"⏳ 等待 {cycle_delay} 秒后开始下一轮...")
            await asyncio.sleep(cycle_delay)

    # 计算总体统计
    overall_end_time = datetime.now()
    total_duration = (overall_end_time - overall_start_time).total_seconds()
    success_rate = (total_successful / total_processed * 100) if total_processed > 0 else 0

    # 生成循环处理报告
    await create_continuous_processing_artifact(
        cycle_count=cycle_count,
        total_processed=total_processed,
        total_successful=total_successful,
        total_failed=total_failed,
        success_rate=success_rate,
        total_duration=total_duration,
        batch_size=batch_size,
        max_concurrent=max_concurrent
    )

    logger.info(f"🎉 循环批量处理完成!")
    logger.info(f"总循环次数: {cycle_count}")
    logger.info(f"总处理任务: {total_processed}")
    logger.info(f"总成功任务: {total_successful}")
    logger.info(f"总失败任务: {total_failed}")
    logger.info(f"总成功率: {success_rate:.1f}%")
    logger.info(f"总耗时: {total_duration:.1f}秒")

    return {
        "status": "completed",
        "cycle_count": cycle_count,
        "total_processed": total_processed,
        "total_successful": total_successful,
        "total_failed": total_failed,
        "success_rate": success_rate,
        "total_duration": total_duration
    }
