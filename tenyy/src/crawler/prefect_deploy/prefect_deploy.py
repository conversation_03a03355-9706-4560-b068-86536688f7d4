#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一的 Prefect 爬虫部署脚本
支持本地和生产环境，通过参数区分环境配置
"""

import os
import subprocess
import sys
import tempfile
import yaml
import argparse
from pathlib import Path

def get_environment_config(env: str):
    """获取环境配置"""
    if env == "local":
        return {
            "prefect_api_url": "http://localhost:4200/api",
            "registry": "localhost:5001",
            "network": "tenyy-net-local",
            "db_config": {
                "POSTGRES_HOST": "app-db",  # Docker 网络中的服务名
                "POSTGRES_PORT": "5432",
                "POSTGRES_USER": "admin",
                "POSTGRES_PASSWORD": os.getenv("POSTGRES_PASSWORD", "your_strong_password_here"),
                "POSTGRES_DB": "tenyy_app",
            },
            "name_suffix": "-local",
            "description_suffix": " - 本地测试版",
            "tags_suffix": ["local"],
            "test_params": True  # 使用测试参数
        }
    else:  # production
        return {
            "prefect_api_url": "http://*************:4200/api",
            "registry": "*************:5000",
            "network": "tenyy-crawler-stack_tenyy-net",  # Swarm网络名称
            "db_config": {
                # 统一使用 app-db，与本地环境保持一致
                "POSTGRES_HOST": "app-db",
                "POSTGRES_PORT": "5432",
                "POSTGRES_USER": "admin",
                "POSTGRES_PASSWORD": "zhangdi168",
                "POSTGRES_DB": "tenyy_app",
            },
            "name_suffix": "",
            "description_suffix": " - 统一入口点",
            "tags_suffix": [],
            "test_params": False  # 使用生产参数
        }

def get_crawler_configs(config):
    """获取爬虫配置"""
    # 基础爬虫定义
    base_crawlers = [
        {
            "name": "yinyongbao-app-crawler",
            "description": "应用宝应用商店爬虫",
            "tags": ["crawler", "yinyongbao", "app"],
            "parameters": {
                "store_type": "yinyongbao",
                "app_type": "app",
                "max_pages": 1 if config["test_params"] else -1,
                "max_categories": 1 if config["test_params"] else -1
            }
        },
        {
            "name": "yinyongbao-game-crawler",
            "description": "应用宝游戏商店爬虫",
            "tags": ["crawler", "yinyongbao", "game"],
            "parameters": {
                "store_type": "yinyongbao",
                "app_type": "game",
                "max_pages": 1 if config["test_params"] else -1,
                "max_categories": 1 if config["test_params"] else -1
            }
        },
        {
            "name": "huawei-app-crawler",
            "description": "华为应用商店爬虫（134个分类）",
            "tags": ["crawler", "huawei"],
            "parameters": {
                "store_type": "huawei",
                "app_type": None,
                "max_pages": 1 if config["test_params"] else -1,
                "max_categories": 1 if config["test_params"] else -1
            }
        },
        {
            "name": "yinyongbao-similar-crawler",
            "description": "应用宝推荐应用爬虫（基于已有包名）",
            "tags": ["crawler", "yinyongbao", "similar"],
            "parameters": {
                "store_type": "yinyongbao_similar",
                "app_type": None,
                "max_pages": -1,  # similar爬虫不使用max_pages
                "max_categories": 1 if config["test_params"] else 0  # 0表示无限制
            }
        }
    ]
    
    # 应用环境特定配置
    crawlers = []
    for crawler in base_crawlers:
        crawler_config = {
            "name": crawler["name"] + config["name_suffix"],
            "description": crawler["description"] + config["description_suffix"],
            "entrypoint": "tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler",
            "env": config["db_config"],
            "tags": crawler["tags"] + config["tags_suffix"],
            "parameters": crawler["parameters"]
        }
        crawlers.append(crawler_config)
    
    return crawlers

def create_work_pool(env: str, config):
    """创建统一工作池"""
    print(f"🔧 创建统一工作池: tenyy-unified-pool ({env} 环境)")

    try:
        # 检查工作池是否已存在
        result = subprocess.run([
            "prefect", "work-pool", "inspect", "tenyy-unified-pool"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 工作池已存在")
            return True
        else:
            # 创建新的统一工作池
            result = subprocess.run([
                "prefect", "work-pool", "create", "tenyy-unified-pool", "--type", "docker"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 工作池创建成功")
                return True
            else:
                print(f"❌ 工作池创建失败: {result.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ 工作池操作失败: {e}")
        return False

def create_prefect_yaml(env: str, config, crawlers):
    """创建prefect.yaml配置"""
    prefect_config = {
        "name": f"tenyy-crawler-{env}",
        "prefect-version": "3.4.7",
        "build": None,
        "push": None,
        "pull": [
            {
                "prefect.deployments.steps.set_working_directory": {
                    "directory": "/app"
                }
            }
        ],
        "deployments": []
    }
    
    # 为每个爬虫创建部署配置
    for crawler in crawlers:
        deployment = {
            "name": crawler["name"],
            "description": crawler["description"],
            "tags": crawler["tags"],
            "schedule": None,
            "entrypoint": crawler["entrypoint"],
            "parameters": crawler["parameters"],
            "work_pool": {
                "name": "tenyy-unified-pool",
                "work_queue_name": "crawler-queue",
                "job_variables": {
                    "image": f"{config['registry']}/tenyy-unified:latest",
                    "auto_remove": True,
                    "networks": [config["network"]],
                    "env": config["db_config"]  # 添加数据库环境变量
                }
            }
        }
        prefect_config["deployments"].append(deployment)
    
    return prefect_config

def deploy_crawlers(env: str, config, crawlers):
    """部署爬虫到Prefect"""
    print(f"🚀 部署爬虫到 {env} 环境...")
    
    # 设置环境变量
    os.environ["PREFECT_API_URL"] = config["prefect_api_url"]
    
    # 创建临时的prefect.yaml文件
    prefect_config = create_prefect_yaml(env, config, crawlers)
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(prefect_config, f, default_flow_style=False)
        temp_yaml_path = f.name
    
    try:
        print(f"📄 使用临时配置文件: {temp_yaml_path}")
        
        # 使用prefect deploy命令部署所有爬虫
        result = subprocess.run([
            "prefect", "deploy",
            "--prefect-file", temp_yaml_path,
            "--all"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 所有爬虫部署成功")
            print(result.stdout)
            return True
        else:
            print(f"❌ 部署失败: {result.stderr}")
            print(f"📄 配置文件内容:")
            with open(temp_yaml_path, 'r') as f:
                print(f.read())
            return False
            
    except Exception as e:
        print(f"❌ 部署过程出错: {e}")
        return False
    finally:
        # 清理临时文件
        os.unlink(temp_yaml_path)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一的Prefect爬虫部署脚本")
    parser.add_argument(
        "environment", 
        choices=["local", "production"],
        help="部署环境"
    )
    
    args = parser.parse_args()
    env = args.environment
    
    print(f"🚀 开始部署到 {env} 环境")
    print(f"📁 工作目录: {Path.cwd()}")
    
    # 获取环境配置
    config = get_environment_config(env)
    crawlers = get_crawler_configs(config)
    
    print(f"🌐 目标服务器: {config['prefect_api_url']}")
    print(f"📦 镜像仓库: {config['registry']}")
    
    # 1. 创建工作池
    if not create_work_pool(env, config):
        sys.exit(1)
    
    # 2. 部署爬虫
    if not deploy_crawlers(env, config, crawlers):
        sys.exit(1)
    
    print(f"✅ {env} 环境部署完成!")
    print(f"\n📋 已部署的爬虫:")
    for crawler in crawlers:
        print(f"  - {crawler['name']}: {crawler['description']}")
    
    ui_url = config["prefect_api_url"].replace("/api", "")
    print(f"\n🌐 访问 Prefect UI: {ui_url}")
    print("💡 在 Deployments 页面可以手动运行爬虫")
    
    if config["test_params"]:
        print("🧪 这是测试版本，使用小的参数值 (max_pages=1, max_categories=1)")
    else:
        print("🏭 这是生产版本，使用完整参数值 (max_pages=-1, max_categories=-1)")

if __name__ == "__main__":
    main()
