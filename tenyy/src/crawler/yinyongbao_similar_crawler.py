#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YinyongbaoSimilarCrawler - 应用宝推荐应用爬虫

基于现有应用宝爬虫，爬取推荐应用数据
通过遍历数据库中的包名，获取每个应用的推荐应用列表
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup

from .yinyongbao_crawler import YinyongbaoCrawler
from ..common.data_writer import get_db_session
from ..models.store_app import StoreApp


class YinyongbaoSimilarCrawler(YinyongbaoCrawler):
    """应用宝推荐应用爬虫"""
    
    def __init__(self, config_name: str = 'yinyongbao'):
        super().__init__(config_name)
        self.similar_base_url = "https://sj.qq.com/similar-app-list/"
        # Similar 爬虫获取的推荐应用本质上还是应用宝的应用，保持 store_type 为 'yinyongbao'
        self.store_type = 'yinyongbao'
        # 但需要一个内部标识来区分是 Similar 爬虫，用于线程池识别
        self._crawler_type = 'yinyongbao_similar'
        
    def get_package_names_from_db(self, limit: Optional[int] = None, offset: int = 0) -> List[str]:
        """
        从数据库获取应用宝商店的包名列表

        Args:
            limit: 限制返回的包名数量，None表示不限制
            offset: 偏移量，用于分页获取

        Returns:
            包名列表
        """
        package_names = []

        try:
            self.logger.info(f"🔍 [DEBUG] 开始从数据库获取包名 - offset: {offset}, limit: {limit or 'unlimited'}")

            with get_db_session() as session:
                # 先检查数据库连接和表是否存在
                # Similar 爬虫查询的是原始的应用宝应用，所以使用 'yinyongbao' 类型
                source_store_type = 'yinyongbao'
                total_count = session.query(StoreApp.app_id).filter(
                    StoreApp.store_type == source_store_type
                ).distinct().count()

                self.logger.info(f"🔍 [DEBUG] 数据库中 {source_store_type} 类型的应用总数: {total_count}")

                if total_count == 0:
                    self.logger.warning(f"⚠️ [DEBUG] 数据库中没有找到 {source_store_type} 类型的应用！")
                    return []

                query = session.query(StoreApp.app_id).filter(
                    StoreApp.store_type == source_store_type
                ).distinct().order_by(StoreApp.app_id)  # 添加排序确保结果一致

                if offset > 0:
                    query = query.offset(offset)

                if limit:
                    query = query.limit(limit)

                results = query.all()
                package_names = [result.app_id for result in results]

                self.logger.info(f"✅ [DEBUG] 成功获取 {len(package_names)} 个包名")
                if package_names:
                    self.logger.info(f"🔍 [DEBUG] 前5个包名示例: {package_names[:5]}")

            return package_names

        except Exception as e:
            self.logger.error(f"❌ [DEBUG] 从数据库获取包名失败: {e}")
            import traceback
            self.logger.error(f"❌ [DEBUG] 详细错误信息: {traceback.format_exc()}")
            return []

    def get_total_package_count(self) -> int:
        """
        获取数据库中应用宝包名的总数量

        Returns:
            包名总数量
        """
        try:
            with get_db_session() as session:
                count = session.query(StoreApp.app_id).filter(
                    StoreApp.store_type == self.store_type
                ).distinct().count()

            self.logger.info(f"Total package count in database: {count}")
            return count

        except Exception as e:
            self.logger.error(f"Failed to get total package count: {e}")
            return 0
    
    def get_categories(self, max_categories: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        重写分类获取方法，返回包名列表作为"分类"
        每个包名被当作一个分类来处理

        Args:
            max_categories: 最大分类数量，None表示不限制

        Returns:
            包名列表，格式化为分类格式
        """
        self.logger.info(f"🚀 [DEBUG] 开始获取分类 - max_categories: {max_categories}")

        # 根据max_categories参数决定是否限制数量
        package_names = self.get_package_names_from_db(limit=max_categories)

        self.logger.info(f"📦 [DEBUG] 从数据库获取到 {len(package_names)} 个包名")

        # 将包名转换为分类格式
        categories = []
        for pkg_name in package_names:
            categories.append({
                'name': f'Similar to {pkg_name}',
                'package_name': pkg_name,
                'type': 'similar'
            })

        self.logger.info(f"✅ [DEBUG] 生成了 {len(categories)} 个分类 (limit: {max_categories or 'unlimited'})")
        if categories:
            self.logger.info(f"🔍 [DEBUG] 前3个分类示例: {[cat['name'] for cat in categories[:3]]}")
        else:
            self.logger.warning(f"⚠️ [DEBUG] 没有生成任何分类！")

        return categories
    
    def scrape_list_page(self, category: Dict[str, Any], page_num: int = 1) -> Tuple[List[Dict[str, Any]], bool]:
        """
        重写列表页爬取方法，通过API获取推荐应用数据

        Args:
            category: 分类信息（包含package_name）
            page_num: 页码（推荐应用只有一页，忽略此参数）

        Returns:
            (应用列表, 是否有下一页)
        """
        package_name = category.get('package_name')
        category_name = category.get('name', 'Unknown')

        self.logger.info(f"🔍 [DEBUG] 开始爬取分类: {category_name}")
        self.logger.info(f"📦 [DEBUG] 目标包名: {package_name}")

        if not package_name:
            self.logger.error("❌ [DEBUG] 分类中没有 package_name!")
            return [], False

        # 推荐应用只有一页，page_num > 1 时直接返回空
        if page_num > 1:
            self.logger.info(f"⏭️ [DEBUG] 页码 {page_num} > 1，跳过（推荐应用只有一页）")
            return [], False

        try:
            self.logger.info(f"🌐 [DEBUG] 开始调用推荐应用API...")
            # 调用推荐应用API
            app_list = self._get_similar_apps_from_api(package_name)

            self.logger.info(f"✅ [DEBUG] 为 {package_name} 找到 {len(app_list)} 个推荐应用")
            if app_list:
                self.logger.info(f"🔍 [DEBUG] 前3个应用示例: {[app.get('pkgName', app.get('pkg_name', 'Unknown')) for app in app_list[:3]]}")
            else:
                self.logger.warning(f"⚠️ [DEBUG] 没有找到任何推荐应用！")

            return app_list, False  # 推荐应用只有一页

        except Exception as e:
            self.logger.error(f"❌ [DEBUG] 爬取 {package_name} 的推荐应用失败: {e}")
            import traceback
            self.logger.error(f"❌ [DEBUG] 详细错误信息: {traceback.format_exc()}")
            return [], False

    def _get_similar_apps_from_api(self, package_name: str) -> List[Dict[str, Any]]:
        """
        从HTML页面获取推荐应用的完整信息（包括通过MD5构建的下载链接）

        Args:
            package_name: 源应用包名

        Returns:
            推荐应用列表
        """
        try:
            self.logger.info(f"🌐 [DEBUG] 开始从HTML页面获取 {package_name} 的推荐应用...")

            # 直接从HTML页面获取推荐应用的完整信息（包括构建的下载链接）
            similar_apps = self._get_similar_package_names_from_html(package_name)

            if not similar_apps:
                self.logger.warning(f"⚠️ [DEBUG] HTML页面中没有找到 {package_name} 的推荐应用")
                return []

            self.logger.info(f"✅ [DEBUG] 从HTML页面找到 {len(similar_apps)} 个推荐应用（包含下载链接）")
            return similar_apps

        except Exception as e:
            self.logger.error(f"❌ [DEBUG] 获取 {package_name} 推荐应用失败: {e}")
            import traceback
            self.logger.error(f"❌ [DEBUG] 详细错误信息: {traceback.format_exc()}")
            return []



    def _get_similar_package_names_from_html(self, package_name: str) -> List[Dict[str, Any]]:
        """从HTML页面提取推荐应用的完整信息（包括下载链接）"""
        try:
            # 访问推荐应用页面
            similar_url = f"{self.similar_base_url}{package_name}"
            self.logger.info(f"🌐 [DEBUG] 访问推荐应用页面: {similar_url}")

            response = self.http_client.session.get(
                similar_url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                },
                timeout=15
            )
            response.raise_for_status()

            self.logger.info(f"✅ [DEBUG] HTTP请求成功，状态码: {response.status_code}")
            self.logger.info(f"📄 [DEBUG] 响应内容长度: {len(response.text)} 字符")

            html_content = response.text

            # 方法1：尝试从 __NEXT_DATA__ 脚本标签中提取
            self.logger.info(f"🔍 [DEBUG] 尝试从 __NEXT_DATA__ 提取应用数据...")
            apps_from_next_data = self._extract_apps_from_next_data(html_content, package_name)
            if apps_from_next_data:
                self.logger.info(f"✅ [DEBUG] 从 __NEXT_DATA__ 找到 {len(apps_from_next_data)} 个应用")
                return apps_from_next_data

            # 方法2：尝试从 dynamicCardResponse 中提取
            self.logger.info(f"🔍 [DEBUG] 尝试从 dynamicCardResponse 提取应用数据...")
            apps_from_dynamic_card = self._extract_apps_from_dynamic_card(html_content, package_name)
            if apps_from_dynamic_card:
                self.logger.info(f"✅ [DEBUG] 从 dynamicCardResponse 找到 {len(apps_from_dynamic_card)} 个应用")
                return apps_from_dynamic_card

            self.logger.warning(f"⚠️ [DEBUG] HTML页面中没有找到 {package_name} 的应用数据")
            # 输出HTML内容的前500字符用于调试
            self.logger.debug(f"🔍 [DEBUG] HTML内容前500字符: {html_content[:500]}")
            return []

        except Exception as e:
            self.logger.error(f"❌ [DEBUG] 从HTML提取应用数据失败: {e}")
            import traceback
            self.logger.error(f"❌ [DEBUG] 详细错误信息: {traceback.format_exc()}")
            return []

    def _extract_apps_from_next_data(self, html_content: str, source_package: str) -> List[Dict[str, Any]]:
        """从 __NEXT_DATA__ 脚本标签中提取应用信息并构建下载链接"""
        try:
            # 查找 __NEXT_DATA__ 脚本标签
            next_data_start = html_content.find('<script id="__NEXT_DATA__"')
            if next_data_start == -1:
                return []

            # 找到脚本内容的开始
            content_start = html_content.find('>', next_data_start) + 1
            content_end = html_content.find('</script>', content_start)

            if content_start == 0 or content_end == -1:
                return []

            json_str = html_content[content_start:content_end].strip()
            next_data = json.loads(json_str)

            # 导航到 props.pageProps.dynamicCardResponse.data.components[0].data
            props = next_data.get('props', {})
            page_props = props.get('pageProps', {})
            dynamic_card_response = page_props.get('dynamicCardResponse', {})
            data = dynamic_card_response.get('data', {})
            components = data.get('components', [])

            apps = []
            for component in components:
                comp_data = component.get('data', {})
                item_data = comp_data.get('itemData', [])

                for item in item_data:
                    pkg_name = item.get('pkg_name')
                    if pkg_name and pkg_name != source_package:
                        # 构建下载链接（使用MD5值）
                        download_url = self._build_download_url_from_md5(
                            item.get('md_5', ''),
                            pkg_name,
                            item.get('version_name', '')
                        )

                        # 构建完整的应用信息
                        app_name = item.get('name', '')
                        app_info = {
                            'pkgName': pkg_name,
                            'pkg_name': pkg_name,  # 添加 pkg_name 字段，确保详情页能正确提取包名
                            'appId': pkg_name,  # 使用包名作为 appId，而不是数字ID
                            'store_id': pkg_name,  # 添加 store_id 字段作为备用
                            'numeric_app_id': item.get('app_id', ''),  # 保留原始数字ID
                            'appName': app_name,
                            'name': app_name,  # 添加 name 字段，确保兼容性
                            'iconUrl': item.get('icon', ''),
                            'categoryName': item.get('tags', ''),
                            'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                            'versionName': item.get('version_name', ''),
                            'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                            'download_url': download_url,  # 通过MD5构建的下载链接
                            'apkUrl': download_url,  # 标准化字段名
                            'md5': item.get('md_5', ''),
                            'source_package': source_package,
                            'is_similar_app': True,
                        }

                        # 只添加能够构建下载链接的应用
                        if download_url:
                            apps.append(app_info)
                            self.logger.debug(f"Built download URL for {pkg_name}: {download_url[:100]}...")
                        else:
                            self.logger.debug(f"Cannot build download URL for {pkg_name} - missing MD5 or version")

            return apps

        except Exception as e:
            self.logger.warning(f"Failed to extract apps from __NEXT_DATA__: {e}")
            return []

    def _build_download_url_from_md5(self, md5_value: str, pkg_name: str, version: str) -> str:
        """
        通过MD5值构建下载链接

        Args:
            md5_value: 应用的MD5值
            pkg_name: 包名
            version: 版本号

        Returns:
            构建的下载链接，如果无法构建则返回空字符串
        """
        if not md5_value or not pkg_name or not version:
            return ''

        # 应用宝下载链接格式
        download_url = f'http://imtt2.dd.qq.com/sjy.00009/sjy.00004/16891/apk/{md5_value}.apk?fsname={pkg_name}_{version}.apk'
        return download_url



    def _extract_apps_from_dynamic_card(self, html_content: str, source_package: str) -> List[Dict[str, Any]]:
        """从 dynamicCardResponse 中提取应用信息（备用方法）"""
        try:
            if '"dynamicCardResponse":' not in html_content:
                return []

            # 提取JSON数据
            start = html_content.find('"dynamicCardResponse":')
            start += len('"dynamicCardResponse":')

            # 简单的JSON提取
            brace_count = 0
            json_end = start

            for i, char in enumerate(html_content[start:], start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break

            json_str = html_content[start:json_end]
            json_data = json.loads(json_str)

            # 提取应用信息
            apps = []
            data = json_data.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})
                item_data = comp_data.get('itemData', [])

                for item in item_data:
                    pkg_name = item.get('pkg_name')
                    if pkg_name and pkg_name != source_package:
                        # 构建完整的应用信息
                        app_info = {
                            'pkgName': pkg_name,
                            'appId': pkg_name,
                            'appName': item.get('name', ''),
                            'iconUrl': item.get('icon', ''),
                            'categoryName': item.get('tags', ''),
                            'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                            'versionName': item.get('version_name', ''),
                            'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                            'download_url': item.get('download_url', ''),  # 关键：下载链接
                            'source_package': source_package,
                            'is_similar_app': True,
                        }

                        # 只添加有下载链接的应用
                        if app_info['download_url'] and app_info['download_url'].startswith(('http://', 'https://')):
                            apps.append(app_info)
                            self.logger.debug(f"Found app with download URL: {pkg_name} -> {app_info['download_url'][:100]}...")
                        else:
                            self.logger.debug(f"Skipping app without download URL: {pkg_name}")

            return apps

        except Exception as e:
            self.logger.warning(f"Failed to extract apps from dynamicCardResponse: {e}")
            return []

    def _get_app_info_from_regular_api(self, pkg_name: str, source_package: str) -> Optional[Dict[str, Any]]:
        """通过常规应用宝API获取应用的完整信息（包括下载链接）"""
        try:
            # 尝试从应用和游戏两个API中查找
            for app_type in ['app', 'game']:
                payload = self._build_search_payload_for_package(pkg_name, app_type)

                response = self.http_client.post(self.api_url, json_data=payload)
                if not response:
                    continue

                json_response = response.json()
                app_info = self._extract_app_from_response(json_response, pkg_name, source_package)

                if app_info:
                    self.logger.debug(f"Found {pkg_name} in {app_type} API with download_url: {bool(app_info.get('download_url'))}")
                    return app_info

            self.logger.warning(f"Could not find complete info for {pkg_name}")
            return None

        except Exception as e:
            self.logger.warning(f"Failed to get app info for {pkg_name}: {e}")
            return None

    def _build_search_payload_for_package(self, pkg_name: str, app_type: str) -> Dict[str, Any]:
        """构建搜索特定包名的API请求"""
        layout = "YYB_HOME_APP_LIBRARY_LIST" if app_type == 'app' else "YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"
        scene = "app_center" if app_type == 'app' else "game_center"
        list_key = "cate_alias" if app_type == 'app' else "tag_alias"

        return {
            "head": {
                "cmd": "dynamicard_yybhome",
                "authInfo": {"businessId": "AuthName"},
                "deviceInfo": {"platform": 2},
                "userInfo": {"guid": "ca6544f9-b0b4-4539-972b-fa1b5c38c942"},
                "expSceneIds": "92170" if app_type == 'game' else "",
                "hostAppInfo": {"scene": scene}
            },
            "body": {
                "bid": "yybhome",
                "offset": 0,
                "size": 50,  # 增加返回数量以提高找到的概率
                "preview": False,
                "listS": {
                    "region": {"repStr": ["CN"]},
                    list_key: {"repStr": ["all"]}  # 搜索所有分类
                },
                "listI": {
                    "limit": {"repInt": [50]},
                    "offset": {"repInt": [0]}
                },
                "layout": layout
            }
        }

    def _extract_app_from_response(self, json_response: Dict[str, Any], target_package: str, source_package: str) -> Optional[Dict[str, Any]]:
        """从API响应中提取特定包名的应用信息"""
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})

                # 尝试不同的数据结构
                for key in ['list', 'apps', 'items', 'data', 'itemData']:
                    if key in comp_data and isinstance(comp_data[key], list):
                        for item in comp_data[key]:
                            # 检查是否是目标应用
                            pkg_name = item.get('pkg_name') or item.get('pkgName') or item.get('appId')
                            if pkg_name == target_package:
                                # 找到了！构建完整的应用信息
                                app_info = item.copy()
                                app_info.update({
                                    'source_package': source_package,
                                    'is_similar_app': True,
                                    'pkgName': pkg_name,
                                    'appId': item.get('app_id', pkg_name),
                                    'appName': item.get('name'),
                                    'iconUrl': item.get('icon'),
                                    'categoryName': item.get('tags', ''),
                                    'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                                    'versionName': item.get('version_name'),
                                    'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                                    'apkUrl': item.get('download_url'),  # 重要：这里应该有下载链接！
                                })

                                download_url = item.get('download_url')
                                if download_url and download_url.startswith(('http://', 'https://')):
                                    self.logger.info(f"Found download URL for {target_package}: {download_url[:100]}...")
                                    return app_info
                                else:
                                    self.logger.warning(f"Found {target_package} but no download URL")
                                    return app_info  # 即使没有下载链接也返回，后续可能会处理

            return None

        except Exception as e:
            self.logger.warning(f"Failed to extract app from response: {e}")
            return None

    def _parse_similar_apps_api_response(self, json_response: Dict[str, Any], source_package: str) -> List[Dict[str, Any]]:
        """
        解析推荐应用API响应

        Args:
            json_response: API响应数据
            source_package: 源应用包名

        Returns:
            应用列表
        """
        apps = []

        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                # 查找推荐应用组件
                card_id = component.get('cardId', '')
                if 'similar' in card_id.lower() or 'game_list' in card_id:
                    comp_data = component.get('data', {})
                    item_data = comp_data.get('itemData', [])

                    for item in item_data:
                        # 跳过源应用本身
                        pkg_name = item.get('pkg_name')
                        if pkg_name == source_package:
                            continue

                        # 添加推荐应用标记
                        app_info = item.copy()
                        app_info.update({
                            'source_package': source_package,
                            'is_similar_app': True,
                            'pkgName': pkg_name,  # 添加标准字段名
                            'appId': item.get('app_id', pkg_name),
                            'appName': item.get('name'),
                            'iconUrl': item.get('icon'),
                            'categoryName': item.get('tags', ''),
                            'score': float(item.get('average_rating', 0)) if item.get('average_rating') else None,
                            'versionName': item.get('version_name'),
                            'apkSize': int(item.get('apk_size', 0)) if item.get('apk_size') else None,
                            'apkUrl': item.get('download_url'),  # 重要：这里有下载链接！
                        })

                        apps.append(app_info)

                        self.logger.debug(f"Found similar app: {pkg_name} with download_url: {item.get('download_url', 'None')}")

            self.logger.info(f"Parsed {len(apps)} similar apps from API response")
            return apps

        except Exception as e:
            self.logger.warning(f"Failed to parse similar apps API response: {e}")
            return []
    
    def _parse_similar_apps_html(self, html: str, source_package: str) -> List[Dict[str, Any]]:
        """
        解析推荐应用HTML页面
        
        Args:
            html: HTML内容
            source_package: 源应用包名
            
        Returns:
            应用列表
        """
        apps = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找应用链接
            app_links = soup.find_all('a', href=re.compile(r'/appdetail/'))
            
            for link in app_links:
                try:
                    # 提取包名
                    href = link.get('href', '')
                    package_match = re.search(r'/appdetail/([^/?]+)', href)
                    if not package_match:
                        continue
                    
                    package_name = package_match.group(1)
                    
                    # 跳过源应用本身
                    if package_name == source_package:
                        continue
                    
                    # 提取应用信息
                    app_info = self._extract_app_info_from_link(link, package_name, source_package)
                    if app_info:
                        apps.append(app_info)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to parse app link: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Failed to parse similar apps HTML: {e}")
        
        return apps
    
    def _extract_app_info_from_link(self, link_element, package_name: str, source_package: str) -> Optional[Dict[str, Any]]:
        """
        从链接元素中提取应用信息
        
        Args:
            link_element: BeautifulSoup链接元素
            package_name: 应用包名
            source_package: 源应用包名
            
        Returns:
            应用信息字典
        """
        try:
            app_info = {
                'pkgName': package_name,
                'appId': package_name,  # 使用包名作为appId
                'source_package': source_package,  # 记录推荐来源
                'is_similar_app': True,  # 标记为推荐应用
            }

            # 提取应用名称
            name_elem = link_element.find('img')
            if name_elem and name_elem.get('alt'):
                # 从alt属性提取应用名称，去掉年份和"官方新版"等后缀
                app_name = name_elem.get('alt', '').strip()
                app_name = re.sub(r'\d{4}官方新版$', '', app_name).strip()
                app_info['appName'] = app_name
                app_info['name'] = app_name  # 添加name字段，与标准化字段对应

            # 提取图标URL
            icon_elem = link_element.find('img')
            if icon_elem and icon_elem.get('src'):
                app_info['iconUrl'] = icon_elem.get('src')
                app_info['icon_url'] = icon_elem.get('src')  # 添加标准化字段名

            # 提取评分（如果有）
            rating_elem = link_element.find(text=re.compile(r'^\d+\.\d+$'))
            if rating_elem:
                try:
                    score = float(rating_elem.strip())
                    app_info['score'] = score
                    app_info['rating'] = score  # 添加标准化字段名
                except ValueError:
                    pass

            # 提取分类信息（如果有）
            category_elems = link_element.find_all(text=True)
            for text in category_elems:
                text = text.strip()
                if text and not text.replace('.', '').isdigit() and len(text) > 1 and text not in ['电脑版', '下载', '扫一扫下载']:
                    if 'categoryName' not in app_info:
                        app_info['categoryName'] = text
                        app_info['category'] = text  # 添加标准化字段名
                        break

            # 注意：推荐应用页面没有直接的下载链接，需要从详情页获取
            # 这里不设置download_url，让详情页来提供

            return app_info
            
        except Exception as e:
            self.logger.warning(f"Failed to extract app info for {package_name}: {e}")
            return None
    
    def get_download_url_from_api(self, package_name: str) -> Optional[str]:
        """
        通过应用宝API获取下载链接

        Args:
            package_name: 应用包名

        Returns:
            下载链接
        """
        try:
            # 尝试通过常规应用宝爬虫的列表页API来获取下载链接
            # 先尝试应用类型的API
            for app_type in ['app', 'game']:
                payload = self._build_search_payload(package_name, app_type)

                # 发送请求
                response = self.http_client.post(self.api_url, json_data=payload)

                if response:
                    json_response = response.json()
                    download_url = self._extract_download_url_from_response(json_response, package_name)
                    if download_url:
                        return download_url

            return None

        except Exception as e:
            self.logger.warning(f"Failed to get download URL for {package_name}: {e}")
            return None

    def _build_search_payload(self, package_name: str, app_type: str) -> Dict[str, Any]:
        """构建搜索特定包名的API请求"""
        layout = "YYB_HOME_APP_LIBRARY_LIST" if app_type == 'app' else "YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"
        scene = "app_center" if app_type == 'app' else "game_center"
        list_key = "cate_alias" if app_type == 'app' else "tag_alias"

        return {
            "head": {
                "cmd": "dynamicard_yybhome",
                "authInfo": {"businessId": "AuthName"},
                "deviceInfo": {"platform": 2},
                "userInfo": {"guid": "ca6544f9-b0b4-4539-972b-fa1b5c38c942"},
                "expSceneIds": "92170" if app_type == 'game' else "",
                "hostAppInfo": {"scene": scene}
            },
            "body": {
                "bid": "yybhome",
                "offset": 0,
                "size": 50,  # 增加返回数量以提高找到的概率
                "preview": False,
                "listS": {
                    "region": {"repStr": ["CN"]},
                    list_key: {"repStr": ["all"]}  # 搜索所有分类
                },
                "listI": {
                    "limit": {"repInt": [50]},
                    "offset": {"repInt": [0]}
                },
                "layout": layout
            }
        }

    def _extract_download_url_from_response(self, json_response: Dict[str, Any], target_package: str) -> Optional[str]:
        """从API响应中提取特定包名的下载链接"""
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                comp_data = component.get('data', {})

                # 尝试不同的数据结构
                for key in ['list', 'apps', 'items', 'data', 'itemData']:
                    if key in comp_data and isinstance(comp_data[key], list):
                        for item in comp_data[key]:
                            # 检查是否是目标应用
                            pkg_name = item.get('pkg_name') or item.get('pkgName') or item.get('appId')
                            if pkg_name == target_package:
                                download_url = item.get('download_url') or item.get('apkUrl')
                                if download_url and download_url.startswith(('http://', 'https://')):
                                    self.logger.info(f"Found download URL for {target_package}")
                                    return download_url

            return None

        except Exception as e:
            self.logger.warning(f"Failed to extract download URL from response: {e}")
            return None


        except Exception as e:
            self.logger.warning(f"Failed to get download URL for {package_name}: {e}")
            return None

    def process_and_standardize(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        重写数据标准化方法，添加推荐应用特有的处理逻辑

        Args:
            raw_data: 原始数据
            **kwargs: 额外参数

        Returns:
            标准化数据
        """
        # 如果没有下载链接，尝试通过API获取
        if not raw_data.get('download_url') and raw_data.get('pkgName'):
            download_url = self.get_download_url_from_api(raw_data['pkgName'])
            if download_url:
                raw_data['download_url'] = download_url

        # 调用父类的标准化方法
        standardized_data = super().process_and_standardize(raw_data, **kwargs)

        # 添加推荐应用特有的元数据
        if raw_data.get('is_similar_app'):
            metadata = standardized_data.get('metadata', {})
            metadata.update({
                'is_similar_app': True,
                'source_package': raw_data.get('source_package'),
                'crawl_type': 'similar'
            })
            standardized_data['metadata'] = metadata

        return standardized_data
