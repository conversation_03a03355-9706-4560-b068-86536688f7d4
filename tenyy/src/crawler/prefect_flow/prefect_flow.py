#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Prefect爬虫流程定义 - 单一入口点模式
优化后的版本：每个爬虫只显示一个主Flow，使用预设参数区分不同类型
"""

import subprocess
import json
import time
from typing import Optional
from prefect import flow
from prefect.artifacts import create_markdown_artifact


def create_final_crawler_artifact(store_type: str, app_type: Optional[str], duration: float, output: str, status: str):
    """Create final crawler execution report artifact"""

    # Parse basic statistics from output
    apps_processed = 0
    success_count = 0

    # Try to extract detailed statistics from output
    new_apps = 0
    new_versions = 0
    version_exists = 0

    try:
        import re
        lines = output.split('\n')

        # Extract statistics from log patterns

        for line in lines:
            # 匹配 "Total apps processed: 20" 格式
            if 'total apps processed:' in line.lower():
                match = re.search(r'total apps processed:\s*(\d+)', line.lower())
                if match:
                    apps_processed = int(match.group(1))
            # 匹配 "Successful: 20" 格式
            elif 'successful:' in line.lower():
                match = re.search(r'successful:\s*(\d+)', line.lower())
                if match:
                    success_count = int(match.group(1))
            # 统计新应用数量
            elif '[New App] Created main record' in line:
                new_apps += 1
            # 统计新版本数量（这是我们真正关心的指标）
            elif '[New Version] Created version record' in line:
                new_versions += 1
            # 统计已存在版本数量
            elif '[Version Exists]' in line:
                version_exists += 1
            # 备用匹配：处理其他可能的格式
            elif 'apps processed' in line.lower() and ':' in line:
                numbers = re.findall(r':\s*(\d+)', line)
                if numbers:
                    apps_processed = int(numbers[0])
    except Exception as e:
        print(f"Warning: Failed to parse statistics from output: {e}")
        pass

    # Calculate additional metrics
    duplicate_rate = (version_exists / apps_processed * 100) if apps_processed > 0 else 0
    discovery_rate = (new_versions / success_count * 100) if success_count > 0 else 0
    failed_count = apps_processed - success_count

    # Create status info
    status_icon = "✅" if status == "success" else "❌"
    status_text = "Success" if status == "success" else "Failed"

    # Format crawler name
    crawler_name = f"{store_type.upper()}"
    if app_type:
        crawler_name += f" {app_type.upper()}"

    # Create simple markdown report
    markdown_content = f"""# {status_icon} {crawler_name} Crawler Report

## 📊 Execution Summary
| Metric | Value |
|:---|:---|
| **Status** | {status_text} |
| **Duration** | {duration:.1f} seconds |
| **Processing Rate** | {(apps_processed/duration):.1f} apps/sec |

## 📈 Processing Results
| Metric | Count | Percentage |
|:---|:---|:---|
| **Total Processed** | {apps_processed} | 100.0% |
| **Successful** | {success_count} | {(success_count/apps_processed*100) if apps_processed > 0 else 0:.1f}% |
| **Failed** | {failed_count} | {(failed_count/apps_processed*100) if apps_processed > 0 else 0:.1f}% |

## 🎯 Data Quality Metrics
| Metric | Count | Percentage |
|:---|:---|:---|
| **New Apps Discovered** | {new_apps} | {(new_apps/apps_processed*100) if apps_processed > 0 else 0:.1f}% |
| **New Versions Found** | {new_versions} | {discovery_rate:.1f}% |
| **Duplicate/Existing** | {version_exists} | {duplicate_rate:.1f}% |
| **Data Efficiency** | {new_versions} | {(new_versions/apps_processed*100) if apps_processed > 0 else 0:.1f}% |

## 📝 Execution Log (First 500 chars)
```
{output[:500]}{'...' if len(output) > 500 else ''}
```
"""

    # Create artifact
    safe_store_type = store_type.replace('_', '-').lower()
    safe_app_type = (app_type or 'default').replace('_', '-').lower()
    artifact_key = f"crawler-report-{safe_store_type}-{safe_app_type}-{int(time.time())}"

    create_markdown_artifact(
        key=artifact_key,
        markdown=markdown_content,
        description=f"{crawler_name} Crawler Report"
    )


@flow(name="Universal Crawler", flow_run_name="{store_type} {app_type} Crawler Run")
def universal_crawler(
    store_type: str,
    app_type: Optional[str] = None,
    max_pages: int = -1,
    max_categories: int = -1
):
    """
    通用爬虫流程 - 单一入口点，支持UI参数输入

    这是所有爬虫的统一入口点，通过参数区分不同的爬虫类型。
    在Prefect UI中，每个爬虫类型将通过部署配置的预设参数来区分。

    Args:
        store_type: 商店类型 ('yinyongbao', 'huawei', 'yinyongbao_similar')
        app_type: 应用类型 ('app', 'game') - 仅用于应用宝
        max_pages: 每个分类最大页数 (-1表示无限制)
        max_categories: 最大分类数量 (-1表示无限制，0表示yinyongbao_similar的无限制)
    """
    start_time = time.time()
    print(f"🚀 Starting {store_type} crawler")
    print(f"📋 Parameters: app_type={app_type}, max_pages={max_pages}, max_categories={max_categories}")

    # 构建命令
    cmd = [
        "python", "/app/tenyy/src/crawler/containerized_crawler.py",
        "--store", store_type,
        "--max-pages", str(max_pages),
        "--max-categories", str(max_categories)
    ]

    # 只有应用宝需要app_type参数
    if app_type and store_type == "yinyongbao":
        cmd.extend(["--app-type", app_type])

    print(f"🔧 Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)

    # 计算执行时间
    end_time = time.time()
    duration = end_time - start_time

    if result.returncode == 0:
        print("✅ Crawler completed successfully")
        print(result.stdout)

        # 创建最终的 artifact（只在最高层创建）
        # 合并 stdout 和 stderr 来获取完整的输出
        combined_output = result.stdout + "\n" + result.stderr
        create_final_crawler_artifact(store_type, app_type, duration, combined_output, "success")

        return {"status": "success", "output": result.stdout}
    else:
        print("❌ Crawler failed")
        print(result.stderr)

        # 即使失败也创建 artifact 记录
        # 合并 stdout 和 stderr 来获取完整的输出
        combined_output = result.stdout + "\n" + result.stderr
        create_final_crawler_artifact(store_type, app_type, duration, combined_output, "failed")

        raise Exception(f"Crawler failed: {result.stderr}")


# ============================================================================
# 辅助函数 - 不作为Flow暴露，仅用于向后兼容或测试
# ============================================================================

def create_yinyongbao_app_params(max_pages: int = -1, max_categories: int = -1):
    """创建应用宝应用爬虫的参数"""
    return {
        "store_type": "yinyongbao",
        "app_type": "app",
        "max_pages": max_pages,
        "max_categories": max_categories
    }

def create_yinyongbao_game_params(max_pages: int = -1, max_categories: int = -1):
    """创建应用宝游戏爬虫的参数"""
    return {
        "store_type": "yinyongbao",
        "app_type": "game",
        "max_pages": max_pages,
        "max_categories": max_categories
    }

def create_huawei_app_params(max_pages: int = -1, max_categories: int = -1):
    """创建华为应用爬虫的参数"""
    return {
        "store_type": "huawei",
        "max_pages": max_pages,
        "max_categories": max_categories
    }

def create_yinyongbao_similar_params(max_categories: int = 0):
    """创建应用宝推荐爬虫的参数"""
    return {
        "store_type": "yinyongbao_similar",
        "max_categories": max_categories
    }


# ============================================================================
# 向后兼容的Flow包装器 - 如果需要保持现有部署配置不变
# 这些Flow使用隐藏命名约定，不会在主要UI中显示
# ============================================================================

# 注意：这些Flow仍然会在UI中显示，如果要完全隐藏，
# 需要在部署配置中只使用 universal_crawler 并通过预设参数区分

@flow(name="[Hidden] Yinyongbao App Crawler")
def yinyongbao_app_crawler(max_pages: int = -1, max_categories: int = -1):
    """应用宝应用爬虫 - 向后兼容包装器"""
    return universal_crawler(**create_yinyongbao_app_params(max_pages, max_categories))

@flow(name="[Hidden] Yinyongbao Game Crawler")
def yinyongbao_game_crawler(max_pages: int = -1, max_categories: int = -1):
    """应用宝游戏爬虫 - 向后兼容包装器"""
    return universal_crawler(**create_yinyongbao_game_params(max_pages, max_categories))

@flow(name="[Hidden] Huawei App Crawler")
def huawei_app_crawler(max_pages: int = -1, max_categories: int = -1):
    """华为应用爬虫 - 向后兼容包装器"""
    return universal_crawler(**create_huawei_app_params(max_pages, max_categories))

@flow(name="[Hidden] Yinyongbao Similar Crawler")
def yinyongbao_similar_crawler(max_categories: int = 0):
    """应用宝推荐爬虫 - 向后兼容包装器"""
    return universal_crawler(**create_yinyongbao_similar_params(max_categories))
