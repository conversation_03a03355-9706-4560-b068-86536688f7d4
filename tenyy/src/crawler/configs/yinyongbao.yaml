# 应用宝爬虫配置

store_type: "yinyongbao"
base_url: "https://yybadaccess.3g.qq.com/v2/dynamicard_yybhome"

# HTTP配置
http:
  timeout: 20
  max_retries: 3
  retry_delay: 1
  pool_connections: 30  # 增加连接池数量以支持并发详情页请求
  pool_maxsize: 50      # 增加最大连接数
  default_headers:
    Accept-Language: "zh-CN,zh;q=0.9,en-US;q=0.7,en;q=0.5"
    Accept-Encoding: "gzip, deflate"
    Content-Type: "text/plain;charset=UTF-8"
    Origin: "https://sj.qq.com"
    Referer: "https://sj.qq.com/"
    Connection: "keep-alive"
    accept: "*/*"
  user_agents:
    - "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
    - "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
    - "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1"
    - "Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"

# 应用类型配置
app_types:
  app:
    layout: "YYB_HOME_APP_LIBRARY_LIST"
    scene: "app_center"
    list_key: "cate_alias"
  game:
    layout: "YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"
    scene: "game_center"
    list_key: "tag_alias"

# 请求配置
request_config:
  # 每页应用数量
  limit_per_page: 24
  # 基础请求体模板
  base_head:
    cmd: "dynamicard_yybhome"
    authInfo:
      businessId: "AuthName"
    deviceInfo:
      platform: 2
    userInfo:
      guid: "ca6544f9-b0b4-4539-972b-fa1b5c38c942"
    expSceneIds: ""
  base_body:
    bid: "yybhome"
    offset: 0
    size: 10
    preview: false
    listS:
      region:
        repStr: ["CN"]
    listI:
      limit:
        repInt: [24]
      offset:
        repInt: [0]

# 数据映射配置
data_mapping:
  # 字段映射规则
  field_mapping:
    pkg_name: "pkgName"
    name: "appName"
    store_id: "appId"
    developer: "authorName"
    category: "categoryName"
    icon: "iconUrl"
    version_name: "versionName"
    file_size: "apkSize"
    download_url: "apkUrl"
    rating: "score"
    description: "appDetailInfo"
    
  # 数据清洗规则
  cleaning_rules:
    # 布尔值字段
    boolean_fields:
      - "isGame"
    # 整数字段
    integer_fields:
      - "apkSize"
      - "appId"
    # 浮点数字段
    float_fields:
      - "score"

# 爬取配置
crawl_config:
  # 页面间延迟（秒）
  page_delay_range: [1, 3]
  # 是否启用详情页爬取
  enable_detail_crawl: true

  # 是否使用容器内进程池（推荐用于大规模爬取）
  use_internal_process_pool: true

  # 容器内进程池工作进程数
  internal_pool_workers: 10
  # 最大页数限制（-1表示无限制）
  max_pages: -1
