#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
容器化爬虫 - 一次完整爬取，一个容器，内部多进程

设计理念：
- 一次完整的爬取任务 = 一个容器
- 容器内部使用进程池并发处理详情页
- 避免频繁容器创建/销毁的开销
"""

import sys
import argparse
import time
import os
from pathlib import Path
from typing import Dict, Any, List
# 移除 flow 导入 - 此文件不再定义 Flow，只作为普通函数被调用

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入原始版本的 process_category_flow（用于独立容器运行）
from tenyy.src.crawler.framework.base_flow import process_category_flow
from tenyy.src.crawler.yinyongbao_crawler import YinyongbaoCrawler
from tenyy.src.crawler.huawei_crawler import HuaweiCrawler
from tenyy.src.crawler.yinyongbao_similar_crawler import YinyongbaoSimilarCrawler

# 移除 artifact 生成 - 只在最高层 Flow 中生成
def create_markdown_artifact(**_kwargs):
    """空函数 - artifacts 只在最高层生成"""
    pass


def run_containerized_crawl(store_type: str, **kwargs):
    """
    运行容器化爬取

    Args:
        store_type: 商店类型 ('huawei', 'yinyongbao', 或 'yinyongbao_similar')
        **kwargs: 其他参数
    """
    print(f"🚀 Starting containerized crawl for {store_type}")
    print(f"📦 Container mode: One container with internal process pool")
    
    start_time = time.time()
    
    try:
        # 初始化爬虫
        if store_type == 'huawei':
            crawler = HuaweiCrawler()
            print("📱 Initialized Huawei crawler")
        elif store_type == 'yinyongbao':
            crawler = YinyongbaoCrawler()
            app_type = kwargs.get('app_type', 'app')
            crawler.set_app_type(app_type)
            print(f"📱 Initialized Yinyongbao crawler (type: {app_type})")
        elif store_type == 'yinyongbao_similar':
            crawler = YinyongbaoSimilarCrawler()
            print("📱 Initialized Yinyongbao Similar Apps crawler")
        else:
            raise ValueError(f"Unsupported store type: {store_type}")
        
        # 获取配置
        max_pages = kwargs.get('max_pages', 1)
        max_categories = kwargs.get('max_categories', -1)  # 默认无限制
        use_internal_pool = crawler.get_config_value('crawl_config.use_internal_process_pool', False)
        pool_workers = crawler.get_config_value('crawl_config.internal_pool_workers', 10)

        print(f"⚙️  Configuration:")
        print(f"   - Max pages per category: {max_pages}")
        print(f"   - Max categories: {max_categories}")
        print(f"   - Internal process pool: {use_internal_pool}")
        print(f"   - Pool workers: {pool_workers}")

        # 获取分类
        print(f"🔍 [DEBUG] Getting categories for store_type: {store_type}")
        if store_type == 'yinyongbao_similar':
            # 对于推荐爬虫，max_categories 参数的含义是最大包名数量
            # 注意：0表示无限制，与其他爬虫的-1不同
            max_packages = max_categories if max_categories != -1 else 0
            max_packages = max_packages if max_packages > 0 else None  # 0转换为None表示无限制
            print(f"🔍 [DEBUG] Similar crawler - max_categories: {max_categories}, max_packages: {max_packages}")
            # 对于推荐爬虫，直接调用带参数的get_categories方法
            all_categories = crawler.get_categories(max_categories=max_packages)  # type: ignore
            categories = all_categories
            print(f"📂 [DEBUG] Processing {len(categories)} package categories (max_packages: {max_packages or 'unlimited'})")
        else:
            all_categories = crawler.get_categories()
            if store_type == 'yinyongbao':
                app_type = kwargs.get('app_type', 'app')
                all_categories = [cat for cat in all_categories if cat.get('app_type') == app_type]

            # 限制分类数量（避免华为134个分类全部处理）
            if max_categories > 0:
                categories = all_categories[:max_categories]
                print(f"📂 Limited to {len(categories)} categories (out of {len(all_categories)} total)")
            else:
                categories = all_categories
                print(f"📂 Processing all {len(categories)} categories")

        if not categories:
            print("⚠️ [DEBUG] No categories found, exiting...")
            print(f"🔍 [DEBUG] Store type: {store_type}, max_categories: {max_categories}")
            return

        print(f"🔍 [DEBUG] First 3 categories: {[cat.get('name', 'Unknown') for cat in categories[:3]]}")
        
        # 处理每个分类
        total_apps = 0
        total_success = 0
        total_categories = len(categories)

        # Progress tracking artifacts disabled - were not recording data correctly
        print(f"Progress tracking disabled for {store_type} crawler")

        # 保存所有结果用于最终报告
        all_category_results = []

        for i, category in enumerate(categories):
            category_name = category['name']
            print(f"\n📁 Processing category {i+1}/{len(categories)}: {category_name}")

            # Progress tracking disabled

            try:
                # 处理分类（使用容器内进程池）
                results = process_category_flow(
                    category=category,
                    crawler=crawler,
                    max_pages=max_pages,
                    is_game=(store_type == 'yinyongbao' and kwargs.get('app_type') == 'game')
                )

                # 保存结果用于最终报告
                all_category_results.extend(results)

                # 统计结果 - 包括 success 和 skipped 都算作成功处理
                category_success = len([r for r in results if r and r.get('status') in ['success', 'skipped']])
                total_apps += len(results)
                total_success += category_success

                # Progress tracking disabled

                print(f"✅ Category completed: {category_success}/{len(results)} apps successful")

            except Exception as e:
                print(f"❌ Category failed: {e}")
                # Progress tracking disabled
                continue

        # Create final artifact
        duration = time.time() - start_time
        success_rate = (total_success / total_apps * 100) if total_apps > 0 else 0

        final_markdown = f"""# 🎉 {store_type.upper()} Crawler Completed!

## 📊 Final Results
- **Total Categories**: {total_categories}
- **Total Apps**: {total_apps}
- **Successful**: {total_success}
- **Success Rate**: {success_rate:.1f}%
- **Total Duration**: {duration:.1f} seconds
- **Average Speed**: {total_apps/duration:.2f} apps/second

## ⏰ Execution Time
- **Start Time**: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}
- **End Time**: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""

        # Create final artifact with unique key
        final_artifact_key = f"crawler-final-{store_type.replace('_', '-').lower()}-{int(time.time())}"
        create_markdown_artifact(
            key=final_artifact_key,
            markdown=final_markdown,
            description=f"{store_type} Crawler Final Results"
        )

        # 移除中间层的 artifact 生成 - 只在最高层生成最终报告
        
        # 计算总体统计
        duration = time.time() - start_time
        success_rate = (total_success / total_apps * 100) if total_apps > 0 else 0
        
        print(f"\n🎉 Containerized crawl completed!")
        print(f"📊 Results:")
        print(f"   - Total apps processed: {total_apps}")
        print(f"   - Successful: {total_success}")
        print(f"   - Success rate: {success_rate:.1f}%")
        print(f"   - Duration: {duration:.1f} seconds")
        print(f"   - Average: {duration/total_apps:.2f} seconds/app" if total_apps > 0 else "")
        
        return {
            'total_apps': total_apps,
            'successful': total_success,
            'success_rate': success_rate,
            'duration': duration
        }
        
    except Exception as e:
        print(f"❌ Containerized crawl failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='容器化爬虫 - 一次完整爬取')
    parser.add_argument('--store', choices=['huawei', 'yinyongbao', 'yinyongbao_similar'], required=True,
                       help='商店类型')
    parser.add_argument('--app-type', choices=['app', 'game'], default='app',
                       help='应用类型（仅用于应用宝）')
    parser.add_argument('--max-pages', type=int, default=1,
                       help='每个分类的最大页数')
    parser.add_argument('--max-categories', type=int, default=-1,
                       help='最大分类数量，-1表示无限制（华为有134个分类）；对于yinyongbao_similar，0表示无限制')

    args = parser.parse_args()
    
    # 运行容器化爬取
    result = run_containerized_crawl(
        store_type=args.store,
        app_type=args.app_type,
        max_pages=args.max_pages,
        max_categories=args.max_categories
    )
    
    if result:
        print(f"\n✅ Crawl completed successfully")
        sys.exit(0)
    else:
        print(f"\n❌ Crawl failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
