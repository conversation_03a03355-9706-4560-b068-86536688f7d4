#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HuaweiCrawler - 华为应用商店爬虫

基于通用爬虫框架的华为应用商店实现
"""

import json
import gzip
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from prefect import get_run_logger

from .framework import BaseCrawler


class HuaweiCrawler(BaseCrawler):
    """华为应用商店爬虫"""
    
    def __init__(self, config_name: str = 'huawei'):
        super().__init__(config_name)
        self.api_url = self.config.get('base_url')
        self.request_config = self.config.get('request_config', {})
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """
        获取华为应用商店的所有分类
        
        Returns:
            分类列表
        """
        categories = []
        
        # 从配置文件中获取分类文件路径
        category_files = self.config.get('categories', {})

        # 处理应用分类
        app_file = category_files.get('app_file')
        if app_file:
            app_file_path = self._resolve_category_file_path(app_file)
            if app_file_path and app_file_path.exists():
                app_categories = self._load_categories_from_file(str(app_file_path), is_game=False)
                categories.extend(app_categories)
            else:
                self.logger.warning(f"App category file not found: {app_file}")

        # 处理游戏分类
        game_file = category_files.get('game_file')
        if game_file:
            game_file_path = self._resolve_category_file_path(game_file)
            if game_file_path and game_file_path.exists():
                game_categories = self._load_categories_from_file(str(game_file_path), is_game=True)
                categories.extend(game_categories)
            else:
                self.logger.warning(f"Game category file not found: {game_file}")
        
        self.logger.info(f"Loaded {len(categories)} categories from Huawei")
        return categories

    def _resolve_category_file_path(self, file_path: str) -> Optional[Path]:
        """
        解析分类文件路径，尝试多个可能的位置

        Args:
            file_path: 配置中的文件路径

        Returns:
            解析后的Path对象，如果找不到返回None
        """
        # 尝试的路径列表
        possible_paths = [
            Path(file_path),  # 相对于当前工作目录
            Path("/app") / file_path,  # 容器中的绝对路径
            Path(__file__).parent.parent.parent / file_path,  # 相对于项目根目录
            Path(__file__).parent.parent.parent.parent / file_path,  # 再上一级目录
            Path(__file__).parent / "huaweiCat" / Path(file_path).name,  # 相对于当前文件目录
        ]

        for path in possible_paths:
            if path.exists():
                self.logger.debug(f"Found category file at: {path}")
                return path

        self.logger.error(f"Category file not found in any of these locations: {[str(p) for p in possible_paths]}")
        return None
    
    def _load_categories_from_file(self, file_path: str, is_game: bool) -> List[Dict[str, Any]]:
        """从JSON文件加载分类信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            categories = []
            tab_info = data.get('tabInfo', [])
            
            for tab in tab_info:
                # 跳过没有子分类的项目
                if not tab.get('tabInfo'):
                    continue
                
                for sub_tab in tab['tabInfo']:
                    category = {
                        'tabId': sub_tab.get('tabId'),
                        'name': f"{tab.get('tabName', '')} - {sub_tab.get('tabName', '')}",
                        'tabName': sub_tab.get('tabName', ''),
                        'parentName': tab.get('tabName', ''),
                        'is_game': is_game,
                        'realTabId': sub_tab.get('realTabId', sub_tab.get('tabId'))
                    }
                    categories.append(category)
            
            return categories
            
        except Exception as e:
            self.logger.error(f"Failed to load categories from {file_path}: {e}")
            return []
    
    def scrape_list_page(self, category: Dict[str, Any], page_num: int) -> Tuple[List[Dict[str, Any]], bool]:
        """
        爬取华为应用商店的列表页

        Args:
            category: 分类信息
            page_num: 页码

        Returns:
            (应用列表, 是否有下一页)
        """
        tab_id = category.get('realTabId') or category.get('tabId')
        if not tab_id:
            self.logger.warning(f"No tabId found for category {category}")
            return [], False

        if not self.api_url:
            self.logger.error("API URL not configured")
            return [], False

        try:
            # 构建华为特有的表单数据
            timestamp = int(time.time() * 1000)
            form_data = self._build_huawei_form_data(tab_id, page_num, timestamp)
            gzipped_data = gzip.compress(form_data.encode('utf-8'))

            # 发送请求
            response = self.http_client.post(
                self.api_url,
                data=gzipped_data,
                headers=self.headers
            )

            if not response:
                return [], False

            # 解压缩响应
            response_text = self.http_client.decompress_response(response)
            json_response = json.loads(response_text)

            # 检查API错误
            if json_response.get('rtnCode') != 0:
                self.logger.warning(f"API returned error: {json_response.get('rtnDesc', 'Unknown error')}")
                return [], False

            # 解析应用列表
            app_list = self._parse_app_list(json_response)

            # 检查是否有下一页
            has_next_page = self._check_has_next_page(json_response, len(app_list))

            self.logger.info(f"Found {len(app_list)} apps on page {page_num} for {category.get('name')}")
            return app_list, has_next_page

        except Exception as e:
            self.logger.error(f"Failed to scrape list page {page_num} for category {category.get('name')}: {e}")
            return [], False
    
    def scrape_detail_page(self, app_id: str) -> Optional[Dict[str, Any]]:
        """
        爬取华为应用的详情页

        Args:
            app_id: 应用ID

        Returns:
            应用详情数据
        """
        if not self.api_url:
            self.logger.error("API URL not configured")
            return None

        try:
            # 首先获取标准详情页数据
            timestamp = int(time.time() * 1000)
            form_data = self._build_huawei_detail_form_data(app_id, timestamp)
            gzipped_data = gzip.compress(form_data.encode('utf-8'))

            # 发送请求
            response = self.http_client.post(
                self.api_url,
                data=gzipped_data,
                headers=self.headers
            )

            if not response:
                return None

            # 解压缩响应
            response_text = self.http_client.decompress_response(response)
            json_response = json.loads(response_text)

            # 检查API错误
            if json_response.get('rtnCode') != 0:
                self.logger.warning(f"Detail API returned error: {json_response.get('rtnDesc', 'Unknown error')}")
                return None

            # 解析详情数据
            detail_data = self._parse_detail_data(json_response)

            if detail_data:
                detail_data['big_detail'] = json_response

                # 尝试获取"关于此应用"页面的额外数据（包含 shelvesTime）
                about_data = self._scrape_about_app_page(app_id)
                if about_data:
                    # 合并"关于此应用"页面的数据
                    detail_data.update(about_data)
                    self.logger.info(f"Successfully merged about app data for {app_id}")

                return detail_data

            return None

        except Exception as e:
            self.logger.error(f"Failed to scrape detail for app {app_id}: {e}")
            return None

    def _scrape_about_app_page(self, app_id: str) -> Optional[Dict[str, Any]]:
        """
        爬取华为应用的"关于此应用"页面，获取 shelvesTime 等额外信息

        Args:
            app_id: 应用ID

        Returns:
            关于此应用页面的数据
        """
        try:
            # 从复杂的应用ID中提取真正的应用ID
            clean_app_id = self._extract_clean_app_id(app_id)
            if not clean_app_id:
                self.logger.debug(f"Could not extract clean app ID from: {app_id}")
                return None

            # 构建"关于此应用"页面的URI
            about_uri = f"aboutThisApp|{clean_app_id}"

            timestamp = int(time.time() * 1000)
            form_data = self._build_huawei_detail_form_data(about_uri, timestamp)
            gzipped_data = gzip.compress(form_data.encode('utf-8'))

            # 发送请求
            response = self.http_client.post(
                self.api_url,
                data=gzipped_data,
                headers=self.headers
            )

            if not response:
                return None

            # 解压缩响应
            response_text = self.http_client.decompress_response(response)
            json_response = json.loads(response_text)

            # 检查API错误
            if json_response.get('rtnCode') != 0:
                self.logger.debug(f"About app API returned error: {json_response.get('rtnDesc', 'Unknown error')}")
                return None

            # 解析"关于此应用"页面数据
            about_data = self._parse_about_app_data(json_response)
            return about_data

        except Exception as e:
            self.logger.debug(f"Failed to scrape about app page for {app_id}: {e}")
            return None

    def _parse_about_app_data(self, json_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析"关于此应用"页面数据，提取 shelvesTime 和 provider 等字段

        Args:
            json_response: API响应的JSON数据

        Returns:
            提取的字段数据
        """
        extracted_data = {}

        try:
            layout_data = json_response.get('layoutData', [])
            for layout in layout_data:
                data_list = layout.get('dataList', [])
                for data_item in data_list:
                    if isinstance(data_item, dict):
                        # 提取 shelvesTime
                        if 'shelvesTime' in data_item:
                            extracted_data['shelvesTime'] = data_item['shelvesTime']
                            self.logger.info(f"Found shelvesTime in about app page: {data_item['shelvesTime']}")

                        # 提取 provider
                        if 'provider' in data_item:
                            extracted_data['provider'] = data_item['provider']
                            self.logger.info(f"Found provider in about app page: {data_item['provider']}")

                        # 提取其他可能有用的字段
                        for field in ['devNameInfo', 'versionName', 'appIntro', 'newFeatures']:
                            if field in data_item:
                                extracted_data[field] = data_item[field]

        except Exception as e:
            self.logger.warning(f"Failed to parse about app data: {e}")

        return extracted_data

    def _extract_clean_app_id(self, app_id: str) -> Optional[str]:
        """
        从复杂的应用ID中提取真正的应用ID

        Args:
            app_id: 原始应用ID，可能包含复杂的参数

        Returns:
            清理后的应用ID
        """
        try:
            # 华为应用ID通常以 app|C 开头，后面跟着应用ID
            if app_id.startswith('app|'):
                # 提取 C 开头的应用ID
                parts = app_id.split('|')
                if len(parts) >= 2:
                    app_part = parts[1]
                    # 进一步分割，提取 C 开头的部分
                    if '__' in app_part:
                        clean_id = app_part.split('__')[0]
                    else:
                        clean_id = app_part.split('?')[0]  # 移除查询参数

                    if clean_id.startswith('C'):
                        return clean_id

            # 如果已经是简单的格式，直接返回
            if app_id.startswith('C') and len(app_id) < 20:
                return app_id

            return None

        except Exception as e:
            self.logger.debug(f"Failed to extract clean app ID from {app_id}: {e}")
            return None

    def _build_huawei_form_data(self, tab_id: str, page_num: int, timestamp: int) -> str:
        """构建华为API需要的表单数据"""
        # 基于原始华为流程的表单数据格式
        form_data = (
            f"apsid=1740377506066&arkMaxVersion=0&arkMinVersion=0&arkSupport=0&brand=samsung&"
            f"channelId=startFromLauncher&clientPackage=com.huawei.appmarket&cno=4010001&code=0200&"
            f"contentPkg=&dataFilterSwitch=&deviceId=8bfe6b3ee196762d51cb5b99cd1968f254697fc7f4ac02cb252b954b856ad526&"
            f"deviceIdRealType=3&deviceIdType=9&"
            f"deviceSpecParams=%7B%22abis%22%3A%22x86_64%2Cx86%2Carm64-v8a%2Carmeabi-v7a%2Carmeabi%22%2C%22deviceFeatures%22%3A%22U%2C0g%2CP%2CB%2Ce%2Cp%2Ca%2Cb%2C04%2C0m%2Cm%2C08%2C03%2CC%2CS%2C0G%2Cq%2CL%2C2%2C6%2CY%2C0M%2Cf%2C1%2C8%2C9%2CO%2CH%2CW%2Cx%2CG%2Co%2C3%2CR%2Cd%2CQ%2Cn%2Cy%2CT%2Cr%2Cu%2C4%2CN%2CM%2C01%2C09%2CV%2C5%2Cc%2CF%2Ct%2C0L%2Ck%2C00%2Cw%2CE%2C02%2CI%2CJ%2Cj%2CD%2CX%2Ccom.google.android.feature.GOOGLE_BUILD%2Ccom.google.android.feature.GOOGLE_EXPERIENCE%2Ccom.google.android.feature.EXCHANGE_6_2%22%2C%22dpi%22%3A320%2C%22glVersion%22%3A%22OpenGL%20ES%203.1%22%2C%22openglExts%22%3A%22%22%2C%22preferLan%22%3A%22zh%22%2C%22usesLibrary%22%3A%225%2C6%2Cn%2CA%2C9%2C2%2Cb%2C1%2C7%2CF%2Cd%2CB%2CC%2Ccom.google.widevine.software.drm%22%7D&"
            f"fid=0&globalTrace=null&gradeLevel=0&gradeType=&hardwareType=0&isSupportPage=1&"
            f"manufacturer=samsung&maxResults=25&method=client.getTabDetail&net=1&oaidTrack=-2&"
            f"osv=9&outside=0&recommendSwitch=1&reqPageNum={page_num}&roamingTime=0&runMode=2&"
            f"serviceType=0&shellApkVer=0&sid=1740393706827&"
            f"sign=h90010905i00000620000000000001000a0000000400100000010000000010000040230b0100011001000%404D19B1DFC50C4644B3E7BF9FE380B4DD&"
            f"thirdPartyPkg=com.huawei.appmarket&translateFlag=1&ts={timestamp}&"
            f"uri={tab_id}%3Faglocation%3D%257B%2522cres%2522%253A%257B%2522lPos%2522%253A2%252C%2522lid%2522%253A%2522914149%2522%252C%2522pos%2522%253A0%252C%2522resid%2522%253A%2522automore%257Cnormalcard%257C914149%257CA03306%2522%252C%2522rest%2522%253A%2522tab%2522%252C%2522tid%2522%253A%2522dist_a036239dc8c04d9985be428c4c0d5d73%2522%257D%252C%2522ftid%2522%253A%2522dist_4c2612bedc2042fe80b184cf22aa5333%2522%2C%2522pres%2522%253A%257B%2522lPos%2522%253A0%252C%2522pos%2522%253A6%252C%2522resid%2522%253A%2522a036239dc8c04d9985be428c4c0d5d73%2522%252C%2522rest%2522%253A%2522tab%2522%252C%2522tid%2522%253A%2522dist_3f8d5e0c095b40cb93c9809b8554856d%2522%257D%257D%26templateId%3D34850d944e844288a90ef5843d7d40ac%26requestId%3D8644e69213b04d7198a5f6db6aab8eba&"
            f"ver=1.1"
        )
        return form_data

    def _build_huawei_detail_form_data(self, detail_id: str, timestamp: int) -> str:
        """构建华为详情页API需要的表单数据"""
        # 基于原始华为流程的详情页表单数据格式
        form_data = (
            f"apsid=1740377506066&arkMaxVersion=0&arkMinVersion=0&arkSupport=0&brand=samsung&"
            f"channelId=startFromLauncher&clientPackage=com.huawei.appmarket&cno=4010001&code=0200&"
            f"contentPkg=&dataFilterSwitch=&deviceId=8bfe6b3ee196762d51cb5b99cd1968f254697fc7f4ac02cb252b954b856ad526&"
            f"deviceIdRealType=3&deviceIdType=9&"
            f"deviceSpecParams=%7B%22abis%22%3A%22x86_64%2Cx86%2Carm64-v8a%2Carmeabi-v7a%2Carmeabi%22%2C%22deviceFeatures%22%3A%22U%2C0g%2CP%2CB%2Ce%2Cp%2Ca%2Cb%2C04%2C0m%2Cm%2C08%2C03%2CC%2CS%2C0G%2Cq%2CL%2C2%2C6%2CY%2C0M%2Cf%2C1%2C8%2C9%2CO%2CH%2CW%2Cx%2CG%2Co%2C3%2CR%2Cd%2CQ%2Cn%2Cy%2CT%2Cr%2Cu%2C4%2CN%2CM%2C01%2C09%2CV%2C5%2Cc%2CF%2Ct%2C0L%2Ck%2C00%2Cw%2CE%2C02%2CI%2CJ%2Cj%2CD%2CX%2Ccom.google.android.feature.GOOGLE_BUILD%2Ccom.google.android.feature.GOOGLE_EXPERIENCE%2Ccom.google.android.feature.EXCHANGE_6_2%22%2C%22dpi%22%3A320%2C%22glVersion%22%3A%22OpenGL%20ES%203.1%22%2C%22openglExts%22%3A%22%22%2C%22preferLan%22%3A%22zh%22%2C%22usesLibrary%22%3A%225%2C6%2Cn%2CA%2C9%2C2%2Cb%2C1%2C7%2CF%2Cd%2CB%2CC%2Ccom.google.widevine.software.drm%22%7D&"
            f"fid=0&globalTrace=null&gradeLevel=0&gradeType=&hardwareType=0&isSupportPage=1&"
            f"manufacturer=samsung&maxResults=25&method=client.getTabDetail&net=1&oaidTrack=-2&"
            f"osv=9&outside=0&recommendSwitch=1&reqPageNum=1&roamingTime=0&runMode=2&"
            f"serviceType=0&shellApkVer=0&sid=1740393706827&"
            f"sign=h90010905i00000620000000000001000a0000000400100000010000000010000040230b0100011001000%404D19B1DFC50C4644B3E7BF9FE380B4DD&"
            f"thirdPartyPkg=com.huawei.appmarket&translateFlag=1&ts={timestamp}&"
            f"uri={detail_id}&ver=1.1"
        )
        return form_data
    
    def _parse_app_list(self, json_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析应用列表"""
        app_list = []
        
        try:
            layout_data = json_response.get('layoutData', [])
            for layout in layout_data:
                data_list = layout.get('dataList', [])
                for item in data_list:
                    if 'detailId' in item:
                        app_list.append(item)
        
        except Exception as e:
            self.logger.warning(f"Failed to parse app list: {e}")
        
        return app_list
    
    def _parse_detail_data(self, json_response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析详情页数据"""
        try:
            layout_data = json_response.get('layoutData', [])
            app_data, header_data = None, None
            all_fields = {}

            # 遍历所有 layout 和 dataList，提取所有可能的字段
            for layout in layout_data:
                data_list = layout.get('dataList', [])
                if data_list and isinstance(data_list, list):
                    for data_item in data_list:
                        if isinstance(data_item, dict):
                            # 收集所有字段
                            all_fields.update(data_item)

                            # 保持原有逻辑，识别主要数据项
                            if 'downurl' in data_item and app_data is None:
                                app_data = data_item
                            if 'developer' in data_item and header_data is None:
                                header_data = data_item

            # 从 json_response 根级别提取可能的字段（如 shelvesTime, updateTime, provider 等）
            root_level_fields = {}
            for key, value in json_response.items():
                # 提取可能有用的根级别字段
                if key in ['shelvesTime', 'updateTime', 'provider', 'publishTime', 'releaseTime', 'createTime']:
                    root_level_fields[key] = value
                    self.logger.debug(f"Found root level field {key}: {value}")

            # 特别检查 layoutData 中的 shelvesTime 和 provider 字段
            layout_data = json_response.get('layoutData', [])
            for layout in layout_data:
                data_list = layout.get('dataList', [])
                for data_item in data_list:
                    if isinstance(data_item, dict):
                        # 检查是否有 shelvesTime 或 provider 字段
                        for field in ['shelvesTime', 'updateTime', 'provider', 'publishTime', 'releaseTime', 'createTime']:
                            if field in data_item and field not in root_level_fields:
                                root_level_fields[field] = data_item[field]
                                self.logger.info(f"Found {field} in layoutData: {data_item[field]}")

            # 如果找到了主要数据，合并所有字段
            if app_data and header_data:
                # 合并所有提取到的字段，确保包含 shelvesTime 和 provider
                merged_data = {**app_data, **header_data, **all_fields, **root_level_fields, 'big_detail': json_response}
                return merged_data
            elif all_fields:
                # 即使没有找到标准的 app_data 和 header_data，也返回所有提取到的字段
                return {**all_fields, **root_level_fields, 'big_detail': json_response}

            return None

        except Exception as e:
            self.logger.warning(f"Failed to parse detail data: {e}")
            return None
    
    def _check_has_next_page(self, json_response: Dict[str, Any], current_count: int) -> bool:
        """检查是否有下一页"""
        # 如果当前页面没有应用，说明没有下一页
        if current_count == 0:
            return False
        
        # 检查响应中的hasNextPage字段
        has_next = json_response.get('hasNextPage', 0)
        if has_next == 1:
            return True
        
        # 如果当前页面的应用数量少于预期，可能没有下一页
        max_results = self.request_config.get('base_payload', {}).get('maxResults', 25)
        if current_count < max_results:
            return False
        
        return True
