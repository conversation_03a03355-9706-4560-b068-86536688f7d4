"""
通用爬虫框架

这个包提供了一个可扩展的爬虫框架，包含：
- BaseCrawler: 爬虫基类
- 配置管理系统
- 数据映射器
- 通用工具函数
- Prefect流程优化
"""

from .base_crawler import BaseCrawler
from .data_mapper import DataMapper
from .http_client import HttpClient
from .config_manager import ConfigManager
from .utils import DataCleaner
from .flow_utils import create_standard_artifacts, StandardCrawlReport
from .base_flow import generic_crawler_flow

__all__ = [
    'BaseCrawler',
    'DataMapper',
    'HttpClient',
    'ConfigManager',
    'DataCleaner',
    'create_standard_artifacts',
    'StandardCrawlReport',
    'generic_crawler_flow'
]
