#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Flow Utils - Prefect流程工具

提供标准化的流程组件和产物生成功能
"""

import pandas as pd
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime
from prefect import task, get_run_logger
from prefect.artifacts import create_markdown_artifact, create_table_artifact


@dataclass
class StandardCrawlReport:
    """标准爬取报告数据结构"""
    total_processed: int = 0
    success_count: int = 0
    failed_count: int = 0
    skipped_count: int = 0
    new_apps: int = 0
    new_versions: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    store_type: str = ""
    categories_processed: List[str] = None
    
    def __post_init__(self):
        if self.categories_processed is None:
            self.categories_processed = []
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """计算运行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_processed == 0:
            return 0.0
        return (self.success_count / self.total_processed) * 100


@task(name="Create Standard Artifacts")
def create_standard_artifacts(results: List[Dict[str, Any]], flow_name: str, 
                            store_type: str = "", extra_info: Dict[str, Any] = None) -> StandardCrawlReport:
    """
    生成标准化的流程产物
    
    Args:
        results: 处理结果列表
        flow_name: 流程名称
        store_type: 商店类型
        extra_info: 额外信息
        
    Returns:
        标准爬取报告
    """
    logger = get_run_logger()
    
    if not results:
        logger.warning("No results to process for artifacts.")
        return StandardCrawlReport(store_type=store_type)
    
    # 过滤有效结果
    valid_results = [r for r in results if r]
    if not valid_results:
        logger.warning("No valid results found.")
        return StandardCrawlReport(store_type=store_type)
    
    # 创建DataFrame进行统计
    df = pd.DataFrame(valid_results)
    
    # 计算统计信息
    total = len(df)
    success = len(df[df['status'] == 'success'])
    skipped = len(df[df['status'] == 'skipped'])
    failed = len(df[df['status'] == 'failure'])
    new_apps = int(df['is_new_app'].sum()) if 'is_new_app' in df.columns else 0
    new_versions = int(df['is_new_version'].sum()) if 'is_new_version' in df.columns else 0

    # 计算额外的数据质量指标
    duplicate_rate = (skipped / total * 100) if total > 0 else 0
    discovery_rate = (new_apps / success * 100) if success > 0 else 0

    # 计算文件大小统计（如果有数据）
    avg_file_size = 0
    total_data_size = 0
    if 'file_size' in df.columns:
        valid_sizes = df['file_size'].dropna()
        if len(valid_sizes) > 0:
            avg_file_size = valid_sizes.mean() / (1024 * 1024)  # 转换为MB
            total_data_size = valid_sizes.sum() / (1024 * 1024 * 1024)  # 转换为GB
    
    # 创建报告对象（success_count 包括 success 和 skipped）
    actual_success = success + skipped
    report = StandardCrawlReport(
        total_processed=total,
        success_count=actual_success,  # 实际成功数量包括 skipped
        failed_count=failed,
        skipped_count=skipped,
        new_apps=new_apps,
        new_versions=new_versions,
        store_type=store_type,
        end_time=datetime.now()
    )
    
    # 生成Markdown报告
    try:
        # 准备额外的统计信息
        stats_info = {
            'duplicate_rate': duplicate_rate,
            'discovery_rate': discovery_rate,
            'avg_file_size': avg_file_size,
            'total_data_size': total_data_size
        }
        markdown = _generate_markdown_report(report, flow_name, extra_info, stats_info)
        logger.info(f"Generated markdown report with {len(markdown)} characters")

        artifact_result = create_markdown_artifact(key="run-summary", markdown=markdown)
        logger.info(f"Created markdown artifact: {artifact_result}")

    except Exception as e:
        logger.error(f"Failed to create markdown artifact: {e}")

    # 如果有失败的应用，创建失败详情表格
    if failed > 0:
        try:
            failed_df = df[df['status'] == 'failure']
            failed_records = failed_df[['store_id', 'name', 'error']].to_dict(orient='records')
            table_result = create_table_artifact(key="failed-apps", table=failed_records)
            logger.info(f"Created failed apps table with {len(failed_records)} entries: {table_result}")
        except Exception as e:
            logger.error(f"Failed to create failed apps table: {e}")

    # 创建成功应用摘要表格
    if success > 0:
        try:
            success_df = df[df['status'] == 'success']
            summary_columns = ['store_id', 'name', 'is_new_app', 'is_new_version']
            available_columns = [col for col in summary_columns if col in success_df.columns]

            if available_columns:
                success_records = success_df[available_columns].head(100).to_dict(orient='records')
                table_result = create_table_artifact(key="success-apps-summary", table=success_records)
                logger.info(f"Created success apps table with {len(success_records)} entries: {table_result}")
        except Exception as e:
            logger.error(f"Failed to create success apps table: {e}")
    
    logger.info(f"Created artifacts for {flow_name}: {total} total, {actual_success} success, {failed} failed")
    return report


def _generate_markdown_report(report: StandardCrawlReport, flow_name: str,
                            extra_info: Dict[str, Any] = None,
                            stats_info: Dict[str, Any] = None) -> str:
    """生成Markdown格式的报告"""

    # 提取统计信息
    duplicate_rate = stats_info.get('duplicate_rate', 0) if stats_info else 0
    discovery_rate = stats_info.get('discovery_rate', 0) if stats_info else 0
    avg_file_size = stats_info.get('avg_file_size', 0) if stats_info else 0
    total_data_size = stats_info.get('total_data_size', 0) if stats_info else 0
    
    # 基础统计表格
    stats_table = f"""| Metric | Count | Percentage |
|:---|:---|:---|
| **Total Processed** | {report.total_processed} | 100.0% |
| **Success** | {report.success_count} | {report.success_count/report.total_processed*100:.1f}% |
| **Failed** | {report.failed_count} | {report.failed_count/report.total_processed*100:.1f}% |
| **Skipped (Exists)** | {report.skipped_count} | {report.skipped_count/report.total_processed*100:.1f}% |
| **New Apps** | {report.new_apps} | - |
| **New Versions** | {report.new_versions} | - |"""

    # 数据质量指标表格
    quality_table = f"""| Quality Metric | Value |
|:---|:---|
| **Duplicate Rate** | {duplicate_rate:.1f}% |
| **Discovery Rate** | {discovery_rate:.1f}% |
| **Data Efficiency** | {(report.new_apps + report.new_versions)/report.total_processed*100:.1f}% |"""

    # 文件统计表格（如果有数据）
    file_stats = ""
    if avg_file_size > 0:
        file_stats = f"""
## 📦 File Statistics
| Metric | Value |
|:---|:---|
| **Average APK Size** | {avg_file_size:.1f} MB |
| **Total Data Processed** | {total_data_size:.2f} GB |
| **Estimated Bandwidth** | {total_data_size/report.duration_seconds*8:.1f} Mbps |""" if report.duration_seconds else ""
    
    # 构建完整报告
    markdown = f"""# {flow_name} Report

## Summary
- **Store Type**: {report.store_type}
- **Execution Time**: {report.end_time.strftime('%Y-%m-%d %H:%M:%S') if report.end_time else 'Unknown'}
- **Success Rate**: {report.success_rate:.1f}%

## 📊 Basic Statistics
{stats_table}

## 🎯 Data Quality Metrics
{quality_table}
{file_stats}
"""
    
    # 添加运行时长信息
    if report.duration_seconds:
        duration_str = f"{report.duration_seconds:.1f} seconds"
        if report.duration_seconds > 60:
            minutes = int(report.duration_seconds // 60)
            seconds = report.duration_seconds % 60
            duration_str = f"{minutes}m {seconds:.1f}s"
        markdown += f"\n- **Duration**: {duration_str}"
    
    # 添加分类信息
    if report.categories_processed:
        categories_str = ", ".join(report.categories_processed)
        markdown += f"\n- **Categories Processed**: {categories_str}"
    
    # 添加额外信息
    if extra_info:
        markdown += "\n\n## Additional Information\n"
        for key, value in extra_info.items():
            markdown += f"- **{key}**: {value}\n"
    
    # 添加性能指标
    if report.total_processed > 0:
        markdown += f"\n\n## Performance Metrics\n"
        if report.duration_seconds:
            apps_per_second = report.total_processed / report.duration_seconds
            markdown += f"- **Processing Rate**: {apps_per_second:.2f} apps/second\n"
        
        if report.success_count > 0:
            markdown += f"- **New App Discovery Rate**: {report.new_apps/report.success_count*100:.1f}%\n"
            markdown += f"- **Version Update Rate**: {report.new_versions/report.success_count*100:.1f}%\n"
    
    return markdown


@task(name="Create Category Artifacts")
def create_category_artifacts(results: List[Dict[str, Any]], category_name: str,
                            category_id: str = "") -> None:
    """
    为单个分类创建中间产物 - 已禁用，只在最高层生成 artifacts

    Args:
        results: 分类处理结果
        category_name: 分类名称
        category_id: 分类ID
    """
    # 使用标准 logging 而不是 Prefect logger
    import logging
    logger = logging.getLogger(__name__)
    print(f"Category artifacts disabled for {category_name} - only final artifacts will be created")
    # 移除所有 artifact 生成代码，只保留日志记录
    return


@task(name="Create Intermediate Page Artifact")
def create_intermediate_artifact(page_results: List[Dict[str, Any]], page_number: int) -> None:
    """
    为单个页面创建中间产物 - 已禁用，只在最高层生成 artifacts

    Args:
        page_results: 页面处理结果
        page_number: 页码
    """
    # 使用标准 logging 而不是 Prefect logger
    import logging
    logger = logging.getLogger(__name__)
    print(f"Page artifacts disabled for page {page_number} - only final artifacts will be created")
    # 移除所有 artifact 生成代码，只保留日志记录
    return
