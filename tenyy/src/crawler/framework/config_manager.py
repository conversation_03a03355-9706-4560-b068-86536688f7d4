#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ConfigManager - 配置管理器

负责加载和管理爬虫配置文件
"""

import os
import yaml
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器，负责加载YAML配置文件"""
    
    _configs = {}  # 配置缓存
    
    @classmethod
    def get_config_dir(cls) -> Path:
        """获取配置文件目录"""
        current_dir = Path(__file__).parent
        config_dir = current_dir.parent / 'configs'
        config_dir.mkdir(exist_ok=True)
        return config_dir
    
    @classmethod
    def load_config(cls, config_name: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            
        Returns:
            配置字典
        """
        if config_name in cls._configs:
            return cls._configs[config_name]
        
        config_path = cls.get_config_dir() / f"{config_name}.yaml"
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 缓存配置
            cls._configs[config_name] = config
            return config
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in config file {config_path}: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load config file {config_path}: {e}")
    
    @classmethod
    def reload_config(cls, config_name: str) -> Dict[str, Any]:
        """
        重新加载配置文件（清除缓存）
        
        Args:
            config_name: 配置文件名
            
        Returns:
            配置字典
        """
        if config_name in cls._configs:
            del cls._configs[config_name]
        return cls.load_config(config_name)
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已加载的配置
        
        Returns:
            所有配置的字典
        """
        return cls._configs.copy()
    
    @classmethod
    def create_sample_config(cls, config_name: str, config_data: Dict[str, Any]) -> None:
        """
        创建示例配置文件
        
        Args:
            config_name: 配置文件名
            config_data: 配置数据
        """
        config_path = cls.get_config_dir() / f"{config_name}.yaml"
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)
