#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Utils - 通用工具函数

提供数据清洗、格式转换等通用功能
"""

import re
import json
from typing import Any, Optional
from prefect import get_run_logger


class DataCleaner:
    """数据清洗工具类"""

    def __init__(self):
        self._logger = None

    @property
    def logger(self):
        """延迟初始化logger"""
        if self._logger is None:
            try:
                self._logger = get_run_logger()
            except:
                # 如果没有Prefect上下文，使用标准logging
                import logging
                self._logger = logging.getLogger(__name__)
        return self._logger
    
    def to_bool(self, value: Any) -> bool:
        """
        转换为布尔值
        
        Args:
            value: 待转换的值
            
        Returns:
            布尔值
        """
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('1', 'true', 'yes', 'on')
        if isinstance(value, (int, float)):
            return bool(value)
        return False
    
    def to_int(self, value: Any) -> Optional[int]:
        """
        转换为整数
        
        Args:
            value: 待转换的值
            
        Returns:
            整数或None
        """
        if value is None:
            return None
        
        try:
            # 如果是字符串，先清理非数字字符
            if isinstance(value, str):
                # 移除常见的非数字字符（如逗号、空格等）
                cleaned = re.sub(r'[^\d.-]', '', value)
                if not cleaned:
                    return None
                return int(float(cleaned))
            
            return int(value)
        except (ValueError, TypeError):
            self.logger.debug(f"Failed to convert '{value}' to int")
            return None
    
    def to_float(self, value: Any) -> Optional[float]:
        """
        转换为浮点数
        
        Args:
            value: 待转换的值
            
        Returns:
            浮点数或None
        """
        if value is None:
            return None
        
        try:
            # 如果是字符串，先清理非数字字符
            if isinstance(value, str):
                # 移除常见的非数字字符（保留小数点）
                cleaned = re.sub(r'[^\d.-]', '', value)
                if not cleaned:
                    return None
                return float(cleaned)
            
            return float(value)
        except (ValueError, TypeError):
            self.logger.debug(f"Failed to convert '{value}' to float")
            return None
    
    def clean_text(self, text: Any) -> Optional[str]:
        """
        清理文本内容
        
        Args:
            text: 待清理的文本
            
        Returns:
            清理后的文本或None
        """
        if not text:
            return None
        
        if not isinstance(text, str):
            text = str(text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除HTML标签（如果有）
        text = re.sub(r'<[^>]+>', '', text)
        
        return text if text else None
    
    def extract_json_object(self, text: str, start_pos: int = 0) -> Optional[str]:
        """
        从文本中提取完整的JSON对象字符串
        
        Args:
            text: 包含JSON的文本
            start_pos: 开始位置
            
        Returns:
            JSON字符串或None
        """
        try:
            brace_count = 0
            in_string = False
            escape = False
            
            for i, c in enumerate(text[start_pos:], start=start_pos):
                if c == '"' and not escape:
                    in_string = not in_string
                elif c == '\\' and in_string:
                    escape = not escape
                    continue
                elif not in_string:
                    if c == '{':
                        brace_count += 1
                    elif c == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            return text[start_pos:i + 1]
                
                escape = False
            
            return None
        except Exception as e:
            self.logger.debug(f"Failed to extract JSON object: {e}")
            return None
    
    def parse_json_safely(self, json_str: str) -> Optional[dict]:
        """
        安全地解析JSON字符串
        
        Args:
            json_str: JSON字符串
            
        Returns:
            解析后的字典或None
        """
        if not json_str:
            return None
        
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            self.logger.debug(f"Failed to parse JSON: {e}")
            return None
    
    def normalize_url(self, url: str, base_url: Optional[str] = None) -> Optional[str]:
        """
        标准化URL
        
        Args:
            url: 原始URL
            base_url: 基础URL（用于相对路径）
            
        Returns:
            标准化后的URL或None
        """
        if not url:
            return None
        
        url = url.strip()
        
        # 如果是相对路径且提供了base_url
        if base_url and not url.startswith(('http://', 'https://')):
            if url.startswith('/'):
                # 绝对路径
                from urllib.parse import urljoin
                return urljoin(base_url, url)
            else:
                # 相对路径
                from urllib.parse import urljoin
                return urljoin(base_url + '/', url)
        
        return url if url.startswith(('http://', 'https://')) else None
    
    def extract_numbers(self, text: str) -> list:
        """
        从文本中提取所有数字
        
        Args:
            text: 文本内容
            
        Returns:
            数字列表
        """
        if not text:
            return []
        
        # 匹配整数和浮点数
        numbers = re.findall(r'-?\d+\.?\d*', str(text))
        result = []
        
        for num in numbers:
            try:
                if '.' in num:
                    result.append(float(num))
                else:
                    result.append(int(num))
            except ValueError:
                continue
        
        return result
    
    def format_file_size(self, size_bytes: Optional[int]) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            格式化后的大小字符串
        """
        if not size_bytes or size_bytes <= 0:
            return "Unknown"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        size = float(size_bytes)
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"
    
    def validate_package_name(self, pkg_name: str) -> bool:
        """
        验证包名格式
        
        Args:
            pkg_name: 包名
            
        Returns:
            是否有效
        """
        if not pkg_name:
            return False
        
        # Android包名格式验证
        pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$'
        return bool(re.match(pattern, pkg_name))
    
    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        if not filename:
            return "unknown"
        
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(illegal_chars, '_', filename)
        
        # 移除多余的空格和点
        sanitized = re.sub(r'[\s.]+', '_', sanitized)
        
        # 限制长度
        return sanitized[:100] if len(sanitized) > 100 else sanitized
