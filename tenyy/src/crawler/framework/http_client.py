#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HttpClient - 通用HTTP客户端

提供统一的HTTP请求处理，包括：
- User-Agent轮换
- 请求重试
- 超时处理
- 代理支持
- 响应解压缩
"""

import requests
import random
import time
import gzip
import brotli
from typing import Dict, Any, Optional, List
from prefect import get_run_logger
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class HttpClient:
    """通用HTTP客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化HTTP客户端

        Args:
            config: HTTP配置
        """
        self._logger = None
        self.config = config
        
        # 默认配置
        self.timeout = config.get('timeout', 20)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1)
        self.user_agents = config.get('user_agents', self._get_default_user_agents())
        self.default_headers = config.get('default_headers', {})
        self.proxies = config.get('proxies', None)
        
        # 创建session并配置连接池
        self.session = requests.Session()

        # 配置连接池大小以避免连接池满的警告
        adapter = HTTPAdapter(
            pool_connections=config.get('pool_connections', 10),
            pool_maxsize=config.get('pool_maxsize', 20),
            max_retries=Retry(
                total=self.max_retries,
                backoff_factor=0.3,
                status_forcelist=[500, 502, 503, 504]
            )
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        if self.proxies:
            self.session.proxies.update(self.proxies)

    @property
    def logger(self):
        """延迟初始化logger"""
        if self._logger is None:
            try:
                from prefect import get_run_logger
                self._logger = get_run_logger()
            except:
                # 如果没有Prefect上下文，使用标准logging
                import logging
                self._logger = logging.getLogger(__name__)
        return self._logger
    
    def _get_default_user_agents(self) -> List[str]:
        """获取默认User-Agent列表"""
        return [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1",
        ]
    
    def _get_random_headers(self, extra_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        生成随机化的请求头
        
        Args:
            extra_headers: 额外的请求头
            
        Returns:
            请求头字典
        """
        headers = self.default_headers.copy()
        
        # 随机User-Agent
        if self.user_agents:
            headers['User-Agent'] = random.choice(self.user_agents)
        
        # 随机Accept-Language
        headers.setdefault('Accept-Language', 
                          f"zh-CN,zh;q=0.{random.randint(8, 9)},en-US;q=0.{random.randint(6, 7)},en;q=0.{random.randint(4, 5)}")
        
        # 合并额外请求头
        if extra_headers:
            headers.update(extra_headers)
        
        return headers
    
    def decompress_response(self, response: requests.Response) -> str:
        """
        解压缩响应内容

        Args:
            response: requests响应对象

        Returns:
            解压缩后的文本内容
        """
        content = response.content
        encoding = response.headers.get('content-encoding', '').lower()


        # 处理压缩内容
        if encoding == 'gzip':
            try:
                content = gzip.decompress(content)
            except Exception as e:
                # 华为API有时会错误地标识响应为gzip但实际不是，这是正常情况
                self.logger.debug(f"Response marked as gzip but decompression failed (content-encoding={encoding}): {e}. Using response.text instead.")
                return response.text
        elif encoding == 'br':
            try:
                content = brotli.decompress(content)
            except Exception as e:
                # 响应标识为brotli但解压失败，回退到普通文本
                self.logger.debug(f"Response marked as brotli but decompression failed: {e}. Using response.text instead.")
                return response.text
        elif encoding:
            # 有其他编码标识但我们不支持
            self.logger.warning(f"Unsupported content-encoding: {encoding}")
            return response.text

        # 没有压缩标识，直接处理文本编码
        try:
            text_encoding = response.encoding or 'utf-8'
            return content.decode(text_encoding)
        except Exception as e:
            self.logger.warning(f"Failed to decode response content: {e}")
            # 最后的备选方案
            return response.text
    
    def request(self, method: str, url: str, headers: Optional[Dict[str, str]] = None, 
                data: Any = None, json_data: Optional[Dict[str, Any]] = None,
                params: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[requests.Response]:
        """
        发送HTTP请求（带重试机制）
        
        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            data: 请求体数据
            json_data: JSON数据
            params: URL参数
            **kwargs: 其他requests参数
            
        Returns:
            响应对象，失败时返回None
        """
        final_headers = self._get_random_headers(headers)
        
        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=final_headers,
                    data=data,
                    json=json_data,
                    params=params,
                    timeout=self.timeout,
                    **kwargs
                )
                response.raise_for_status()
                
                self.logger.debug(f"Request successful: {method} {url} -> {response.status_code}")
                return response
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                    self.logger.info(f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
                else:
                    self.logger.error(f"All {self.max_retries + 1} attempts failed for {method} {url}")
        
        return None
    
    def get(self, url: str, **kwargs) -> Optional[requests.Response]:
        """GET请求"""
        return self.request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> Optional[requests.Response]:
        """POST请求"""
        return self.request('POST', url, **kwargs)
    
    def put(self, url: str, **kwargs) -> Optional[requests.Response]:
        """PUT请求"""
        return self.request('PUT', url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> Optional[requests.Response]:
        """DELETE请求"""
        return self.request('DELETE', url, **kwargs)
