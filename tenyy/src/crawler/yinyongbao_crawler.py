#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YinyongbaoCrawler - 应用宝爬虫

基于通用爬虫框架的应用宝实现
"""

import json
from typing import List, Dict, Any, Optional, Tuple, Literal

from .framework import BaseCrawler


class YinyongbaoCrawler(BaseCrawler):
    """应用宝爬虫"""
    
    def __init__(self, config_name: str = 'yinyongbao'):
        super().__init__(config_name)
        self.api_url = self.config.get('base_url')
        self.app_types_config = self.config.get('app_types', {})
        self.request_config = self.config.get('request_config', {})
        
        # 当前处理的应用类型
        self.current_app_type = None
    
    def set_app_type(self, app_type: Literal['app', 'game']):
        """设置当前处理的应用类型"""
        self.current_app_type = app_type
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """
        获取应用宝的分类（实际上是应用类型）
        
        Returns:
            分类列表
        """
        categories = []
        
        for app_type, config in self.app_types_config.items():
            category = {
                'app_type': app_type,
                'name': f"应用宝{app_type.upper()}",
                'layout': config.get('layout'),
                'scene': config.get('scene'),
                'list_key': config.get('list_key'),
                'is_game': app_type == 'game'
            }
            categories.append(category)
        
        self.logger.info(f"Loaded {len(categories)} app types from Yinyongbao")
        return categories
    
    def scrape_list_page(self, category: Dict[str, Any], page_num: int) -> Tuple[List[Dict[str, Any]], bool]:
        """
        爬取应用宝的列表页
        
        Args:
            category: 分类信息（实际是应用类型）
            page_num: 页码（从0开始）
            
        Returns:
            (应用列表, 是否有下一页)
        """
        app_type = category.get('app_type')
        if not app_type:
            self.logger.warning(f"No app_type found in category {category}")
            return [], False
        
        # 设置当前应用类型
        self.set_app_type(app_type)
        
        # 构建请求数据
        payload = self._build_request_payload(app_type, page_num)
        
        try:
            # 发送请求
            if not self.api_url:
                self.logger.error("API URL not configured")
                return [], False

            response = self.http_client.post(
                self.api_url,
                json_data=payload
            )
            
            if not response:
                return [], False
            
            json_response = response.json()
            
            # 解析应用列表
            app_list = self._parse_app_list(json_response)

            # 检查是否有下一页
            has_next_page = self._check_has_next_page(json_response, len(app_list))
            
            self.logger.info(f"Found {len(app_list)} {app_type}s on page {page_num}")
            return app_list, has_next_page
            
        except Exception as e:
            self.logger.error(f"Failed to scrape {app_type} list page {page_num}: {e}")
            return [], False
    
    def scrape_detail_page(self, app_id) -> Optional[Dict[str, Any]]:
        """
        爬取应用宝详情页

        Args:
            app_id: 应用包名或应用数据字典

        Returns:
            详情页数据
        """
        # 处理不同类型的输入参数
        if isinstance(app_id, dict):
            # 如果传入的是字典，提取包名
            pkg_name = app_id.get('pkg_name') or app_id.get('appId') or app_id.get('store_id')
            if not pkg_name:
                self.logger.error(f"Cannot extract package name from app data: {list(app_id.keys())}")
                return None
            app_id = pkg_name
        elif not app_id:
            return None

        try:
            # 构建详情页URL
            detail_url = f"https://sj.qq.com/appdetail/{app_id}"

            # 使用桌面浏览器头部
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.7",
                "Cache-Control": "max-age=0",
                "Connection": "keep-alive",
                "Referer": "https://sj.qq.com/category/0.htm",
            }

            # 发送请求
            response = self.http_client.session.get(detail_url, headers=headers, timeout=15)
            response.raise_for_status()

            # 提取dynamicCardResponse数据
            html = response.text
            key = '"dynamicCardResponse":'
            start = html.find(key)

            if start == -1:
                self.logger.warning(f"dynamicCardResponse not found for {app_id}")
                return None

            start += len(key)
            json_str = self._extract_json_object(html, start)

            if not json_str:
                self.logger.error(f"Failed to extract dynamicCardResponse for {app_id}")
                return None

            detail_response = json.loads(json_str)

            # 解析详情页数据
            return self._parse_detail_data(detail_response)

        except Exception as e:
            self.logger.error(f"Failed to scrape detail page for {app_id}: {e}")
            return None
    
    def _build_request_payload(self, app_type: Literal['app', 'game'], page_num: int) -> Dict[str, Any]:
        """构建请求载荷"""
        app_config = self.app_types_config.get(app_type, {})
        base_head = self.request_config.get('base_head', {}).copy()
        base_body = self.request_config.get('base_body', {}).copy()
        limit_per_page = self.request_config.get('limit_per_page', 24)
        
        # 计算偏移量 (page_num从1开始，需要转换为从0开始的offset)
        offset = (page_num - 1) * limit_per_page
        
        # 构建请求头
        head = base_head.copy()
        head['hostAppInfo'] = {'scene': app_config.get('scene', 'app_center')}
        
        if app_type == 'game':
            head['expSceneIds'] = '92170'
        
        # 构建请求体
        body = base_body.copy()
        body['layout'] = app_config.get('layout')
        body['listI']['offset'] = {'repInt': [offset]}
        
        # 设置列表过滤条件
        list_key = app_config.get('list_key', 'cate_alias')
        body['listS'][list_key] = {'repStr': ['all']}
        
        return {
            'head': head,
            'body': body
        }
    
    def _parse_app_list(self, json_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析应用列表"""
        app_list = []
        
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])
            
            for component in components:
                # 查找包含应用数据的组件
                if self._is_app_component(component):
                    apps = self._extract_apps_from_component(component)
                    app_list.extend(apps)
        
        except Exception as e:
            self.logger.warning(f"Failed to parse app list: {e}")
        
        return app_list
    
    def _is_app_component(self, component: Dict[str, Any]) -> bool:
        """判断组件是否包含应用数据"""
        # 检查组件是否有应用相关的字段
        if 'data' not in component:
            return False
        
        data = component['data']
        
        # 查找包含应用信息的数据结构
        if isinstance(data, dict):
            # 检查是否有应用列表相关的键
            for key in ['list', 'apps', 'items', 'data', 'itemData']:
                if key in data and isinstance(data[key], list):
                    # 检查列表中的第一个元素是否像应用数据
                    if data[key] and self._looks_like_app_data(data[key][0]):
                        return True
        
        return False
    
    def _looks_like_app_data(self, item: Dict[str, Any]) -> bool:
        """判断数据项是否像应用数据"""
        # 检查是否包含应用的关键字段（应用宝API的字段名）
        app_fields = ['appName', 'pkgName', 'appId', 'iconUrl', 'apkUrl', 'pkg_name', 'app_id', 'name', 'icon']
        return any(field in item for field in app_fields)
    
    def _extract_apps_from_component(self, component: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从组件中提取应用数据"""
        apps = []
        data = component.get('data', {})
        
        # 尝试不同的数据结构
        for key in ['list', 'apps', 'items', 'data', 'itemData']:
            if key in data and isinstance(data[key], list):
                for item in data[key]:
                    if self._looks_like_app_data(item):
                        # 添加应用类型信息
                        app_data = item.copy()
                        if self.current_app_type:
                            app_data['is_game'] = self.current_app_type == 'game'
                        apps.append(app_data)
        
        return apps

    def _extract_json_object(self, text: str, start_pos: int) -> Optional[str]:
        """从HTML中提取完整的JSON对象"""
        brace_count = 0
        in_string = False
        escape = False

        for i, c in enumerate(text[start_pos:], start=start_pos):
            if c == '"' and not escape:
                in_string = not in_string
            if not in_string:
                if c == '{':
                    brace_count += 1
                elif c == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        return text[start_pos:i+1]
            if c == '\\' and not escape:
                escape = True
            else:
                escape = False

        return None

    def _parse_detail_data(self, detail_response: Dict[str, Any]) -> Dict[str, Any]:
        """解析详情页响应数据"""
        detail_data = {}

        try:
            data = detail_response.get('data', {})
            components = data.get('components', [])

            for component in components:
                if component.get('cardId') == 'yybn_game_basic_info':
                    comp_data = component.get('data', {})
                    if 'itemData' in comp_data and comp_data['itemData']:
                        detail_data = comp_data['itemData'][0]
                        break

        except Exception as e:
            self.logger.warning(f"Failed to parse detail data: {e}")

        return detail_data

    def process_and_standardize(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        重写数据标准化方法，添加应用宝特有的处理逻辑

        Args:
            raw_data: 原始数据（合并后的列表页+详情页数据）
            **kwargs: 额外参数

        Returns:
            标准化数据
        """
        # 从原始数据中获取is_game信息
        is_game = raw_data.get('is_game', kwargs.get('is_game', False))
        kwargs['is_game'] = is_game

        # 如果raw_data中包含detail_data，将其传递给映射器
        if 'detail_data' in raw_data:
            kwargs['detail_data'] = raw_data['detail_data']

        # 调用父类的标准化方法
        return super().process_and_standardize(raw_data, **kwargs)

    def _check_has_next_page(self, json_response: Dict[str, Any], current_count: int) -> bool:
        """
        检查是否有下一页

        Args:
            json_response: API响应数据
            current_count: 当前页面应用数量

        Returns:
            是否有下一页
        """
        # 如果当前页面没有应用，说明没有下一页
        if current_count == 0:
            self.logger.info("Current page has 0 apps, no next page")
            return False

        # 尝试从API响应中查找分页相关信息
        try:
            data = json_response.get('data', {})
            components = data.get('components', [])

            # 查找可能包含分页信息的组件
            for component in components:
                component_data = component.get('data', {})
                # 检查是否有分页相关字段
                if 'hasMore' in component_data:
                    has_more = component_data.get('hasMore', False)
                    self.logger.info(f"Found hasMore field: {has_more}")
                    return bool(has_more)
                elif 'hasNextPage' in component_data:
                    has_next = component_data.get('hasNextPage', 0)
                    self.logger.info(f"Found hasNextPage field: {has_next}")
                    return bool(has_next)
                elif 'isEnd' in component_data:
                    is_end = component_data.get('isEnd', False)
                    self.logger.info(f"Found isEnd field: {is_end}")
                    return not bool(is_end)
        except Exception as e:
            self.logger.warning(f"Failed to check pagination info from response: {e}")

        # 如果没有找到明确的分页信息，且当前页面有应用，就继续翻页
        # 让API自然地返回空结果来结束翻页
        self.logger.info(f"Current page has {current_count} apps, no explicit pagination info found, continuing to next page")
        return True
