#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库备份 Prefect Flow
用于在容器环境中执行数据库备份任务
"""

import subprocess
from typing import Optional
from prefect import flow, task, get_run_logger


@task
def run_backup_script(backup_type: str, backup_dir: Optional[str] = None) -> str:
    """
    在容器中执行备份脚本
    
    Args:
        backup_type: 备份类型 ("base", "incremental", "wal")
        backup_dir: 备份目录路径（可选）
        
    Returns:
        脚本执行输出
    """
    logger = get_run_logger()
    
    # 构建命令
    cmd = ["/app/tenyy/src/backup_plan/pg_backup_production.sh", backup_type]
    
    # 设置环境变量
    env = dict()
    if backup_dir:
        env["BACKUP_DIR"] = backup_dir
    
    logger.info(f"执行备份命令: {' '.join(cmd)}")
    if env:
        logger.info(f"环境变量: {env}")
    
    # 执行备份脚本
    result = subprocess.run(
        cmd, 
        capture_output=True, 
        text=True,
        env=env if env else None
    )
    
    if result.returncode != 0:
        logger.error(f"备份失败: {result.stderr}")
        raise Exception(f"备份失败: {result.stderr}")
    
    logger.info("备份成功完成")
    return result.stdout


@task
def run_restore_script(backup_file: str) -> str:
    """
    在容器中执行恢复脚本
    
    Args:
        backup_file: 备份文件路径
        
    Returns:
        脚本执行输出
    """
    logger = get_run_logger()
    
    # 构建命令
    cmd = ["/app/tenyy/src/backup_plan/restore_db.sh", backup_file]
    
    logger.info(f"执行恢复命令: {' '.join(cmd)}")
    
    # 执行恢复脚本
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logger.error(f"恢复失败: {result.stderr}")
        raise Exception(f"恢复失败: {result.stderr}")
    
    logger.info("恢复成功完成")
    return result.stdout


@flow(name="Database Backup")
def database_backup(
    backup_type: str = "base",
    backup_dir: Optional[str] = None
) -> str:
    """
    数据库备份任务
    
    Args:
        backup_type: 备份类型 ("base", "incremental", "wal")
        backup_dir: 备份目录路径（可选）
        
    Returns:
        备份执行结果
    """
    logger = get_run_logger()
    logger.info(f"开始执行数据库备份任务: {backup_type}")
    
    if backup_dir:
        logger.info(f"备份目录: {backup_dir}")
    
    result = run_backup_script(backup_type, backup_dir)
    logger.info("数据库备份任务完成")
    return result


@flow(name="Database Restore")
def database_restore(backup_file: str) -> str:
    """
    数据库恢复任务
    
    Args:
        backup_file: 备份文件路径
        
    Returns:
        恢复执行结果
    """
    logger = get_run_logger()
    logger.info(f"开始执行数据库恢复任务: {backup_file}")
    
    result = run_restore_script(backup_file)
    logger.info("数据库恢复任务完成")
    return result