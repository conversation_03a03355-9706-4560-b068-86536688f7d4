import os
from pathlib import Path
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    """
    统一管理应用的所有配置。
    - 优先从环境变量加载配置。
    - 如果环境变量未设置，则从 .env 文件加载。
    - 如果 .env 文件中也未设置，则使用代码中定义的默认值。
    """

    # --- 数据库配置 ---
    # 支持两套环境变量命名：postgres_* 和 DB_*
    # 为本地开发提供明智的默认值，这些值可以被 .env 文件覆盖
    postgres_host: str = os.getenv('DB_HOST', 'localhost')  # 支持DB_HOST环境变量
    postgres_port: int = int(os.getenv('DB_PORT', '5434'))  # 支持DB_PORT环境变量，本地开发端口
    postgres_user: str = os.getenv('DB_USER', 'admin')  # 支持DB_USER环境变量
    postgres_password: str = os.getenv('DB_PASSWORD', 'your_strong_password_here')  # 支持DB_PASSWORD环境变量
    postgres_db: str = os.getenv('DB_NAME', 'tenyy_app')  # 支持DB_NAME环境变量


    
    # --- 存储路径 ---
    download_temp_path: str = "storage/temp"



    # --- Aria2 RPC (用于Docker环境) ---
    aria2_rpc_host: str = "aria2"
    aria2_rpc_port: int = 6800
    aria2_rpc_secret: str = "your_secret_key"

    # --- 日志 ---
    log_file_path: str = "slog.txt"

    @property
    def DATABASE_URL(self) -> str:
        """生成 SQLAlchemy 使用的数据库连接字符串。"""
        return f"postgresql+psycopg2://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"



    # Pydantic V2 的标准配置方式
    model_config = SettingsConfigDict(
        # 通过 pathlib 精确构造 .env 文件的绝对路径，确保总能找到
        env_file=Path(__file__).resolve().parent.parent.parent.parent / '.env',
        env_file_encoding='utf-8',
        # 允许在 .env 文件之外的环境变量覆盖配置
        case_sensitive=False,
        # 如果 .env 中有额外的变量，则忽略它们
        extra='ignore'
    )

# 创建全局唯一的配置实例
settings = Settings()

