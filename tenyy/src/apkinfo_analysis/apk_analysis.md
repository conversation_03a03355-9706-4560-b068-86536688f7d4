# APK SDK 自动化分析与识别方案（v1.1）

本方案旨在构建一个高效、可扩展且具备自学习能力的自动化SDK识别系统。核心思想是将 **数据采集** 与 **数据匹配** 彻底解耦，通过引入专门的数据表和优化的工作流，实现极致的匹配效率和天然的回溯能力。

---

## 核心架构

系统由两个独立的 Prefect 流程和一个核心知识库构成：

1.  **`apk_analysis_flow` (APK分析与包名提纯流程)**：从数据库中已存储的APK分析数据中提取并通过智能过滤规则提纯出高质量的"候选包名"，存入 `class_app_discovered_packages` 表。
2.  **`sdk_matching_flow` (SDK匹配与关联流程)**：核心匹配引擎。它不直接处理APK，而是高效地在数据库层面完成 `class_app_discovered_packages` 与 `class_sdk_knowledge_base` 的匹配，并更新最终结果。
3.  **`knowledge_base_enrichment_flow` (知识库增强流程)**：后台的自学习引擎。它利用大语言模型（LLM）分析未知的包名，持续扩充 `class_sdk_knowledge_base`。

这种设计确保了新知识能自动应用于所有历史数据，实现了"一次学习，处处生效"。


---

## 第一阶段：APK分析与包名提纯 (`apk_analysis_flow`)

此流程是数据采集的入口，其目标是从数据库中已存储的APK分析数据中提取出最有可能代表第三方SDK的"候选包名"。

**重要说明**：此流程不涉及APK文件的下载和解压，而是直接从数据库中的 `app_version` 表获取已分析的APK数据。

### 1.1 数据源获取

从 `app_version` 表中获取已分析的APK数据：
-   读取 `android_manifest` 字段：获取应用的主包名（`package`）和权限等信息
-   读取 `packages_class` 字段：获取所有已提取的Java包名列表
-   确保 `analyze_status` 为 `done`，表示APK已完成分析

### 1.2 数据预处理

对从数据库获取的包名数据进行预处理：
-   验证 `packages_class` 字段的数据完整性
-   解析JSON格式的包名列表
-   去除重复的包名
-   按字典序排序包名列表

### 1.3 智能过滤与提纯

为了保证存入 `class_app_discovered_packages` 的数据质量，避免后续匹配的性能瓶颈和结果噪音，必须对从数据库获取的包名进行严格的"智能过滤"：

**核心过滤原则**：本方案的最终目的是识别应用使用的潜在云服务。因此，过滤规则（尤其是黑名单）的设计必须服务于此目标。**黑名单应专注于排除那些明确与具体业务功能无关的、底层的、系统级的包，而绝不能"误杀"任何可能与云服务厂商相关的包**。例如，`com.google.android.gms` 包含Google移动服务，其中可能涉及云消息、认证等，这类包不应被一概排除。我们的目标是"提纯"而非"清洗"，保留所有潜在的信号是关键。

-   **排除系统与标准库**：利用一个精确维护的"包名黑名单"，过滤掉所有明确非功能性SDK的包。这个黑名单是持续更新的，以保证最高的过滤准确性。

    **黑名单分类与示例**：

    *   **Android核心框架**：
        *   `android.*`
        *   `androidx.*`
        *   `com.android.*`

    *   **Java/Kotlin标准库与核心组件**：
        *   `java.*`
        *   `javax.*`
        *   `kotlin.*`
        *   `kotlinx.*`
        *   `dalvik.*`

    *   **常见编程语言与工具库**：
        *   `org.apache.*` (如 `org.apache.http`, `org.apache.commons`)
        *   `org.json`
        *   `org.xml.*`
        *   `org.w3c.*`

    *   **通用Google库 (非功能性SDK)**：
        *   `com.google.gson`
        *   `com.google.guava`
        *   `com.google.protobuf`
        *   `com.google.common.*`

    *   **测试与调试框架**：
        *   `junit.*`
        *   `org.junit.*`
        *   `org.hamcrest.*`
        *   `com.squareup.leakcanary`

-   **排除应用自身包**：根据 `android_manifest` 中解析出的主包名，排除所有应用自身的代码包。
-   **排除疑似混淆包**：通过一组启发式规则，识别并排除可能是ProGuard等工具混淆后的无意义包名。
    -   **短命名规则**：包名的任何部分（如 `a`, `b1`）如果长度小于等于2，且不在一个"已知缩写白名单"中（如 `ad`, `ui`, `io`），则视为混淆。
    -   **无意义结构规则**：如 `a/b/c/d` 这样由单字母重复组成的路径，直接判定为混淆。
    -   **常见混淆库模式**：排除已知的开源混淆库或加固厂商的特定包名模式。

### 1.4 数据存储

将过滤后的高质量候选包名批量存入 `class_app_discovered_packages` 表：
-   每个候选包名对应一条记录
-   记录包含 `app_version_id` 和 `package_name`
-   使用批量插入优化性能
-   避免重复插入已存在的记录

**此阶段产出**：一个高质量的"候选包名"列表，这些包名将被存入 `class_app_discovered_packages` 表，等待下一步的匹配。

**流程特点**：
-   ✅ **纯数据库操作**：无需文件系统操作，只涉及数据库读写
-   ✅ **高效批处理**：可以批量处理多个APK版本的数据
-   ✅ **幂等性**：重复运行不会产生重复数据
-   ✅ **可恢复性**：支持断点续传，可以从任意位置重新开始

### 1.5 Prefect UI 配置参数

在 Prefect UI 中，可以为 `apk_analysis_flow` 配置以下参数：

-   **分析包的数量**：控制每次流程运行时处理的APK数量，默认为1000个
    -   参数名：`batch_size`
    -   默认值：`1000`
    -   可在UI中修改以适应不同性能需求

-   **黑名单配置**：可在配置文件中自定义黑名单规则
    -   配置文件位于：`tenyy/src/config/sdk_blacklist.conf`
    -   支持动态更新，无需重启服务即可生效

### 1.6 Artifact 输出

流程执行完成后会生成详细的执行报告，包括：
-   处理的APK数量统计
-   提取的候选包名数量
-   过滤掉的包名数量及分类统计
-   执行时间及性能指标

---

## 第二阶段：数据库设计

为了支撑解耦的架构，我们需要三张核心表。

### 2.1 应用发现包表 (`class_app_discovered_packages`)

这张表是解耦架构的核心，用于存储从每个APK版本中提取并提纯后的所有候选包名。

**更新后的表结构**：

| 字段名 | 类型 | 索引 | 注释 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | 主键 | 自增ID |
| `app_version_id` | `INTEGER` | ✅ | 外键，关联到 `app_version.id` |
| `package_name` | `VARCHAR(255)` | ✅ | 提纯后的候选包名 |
| `last_checked` | `TIMESTAMP` | ✅ | 标记该候选包名最后一次匹配时间

**设计考量**：
-   **数据量**：这张表的规模会很大，但通过在 `app_version_id` 和 `package_name` 上建立索引，可以保证查询性能。
-   **职责单一**：它只负责存储"发现了什么"，不关心"这是什么"。

### 2.2 SDK知识库表 (`class_sdk_knowledge_base`)

系统的"大脑"，存储所有已识别的SDK信息，并管理待分析的未知包名。

**表结构 (`class_sdk_knowledge_base`):**

| 字段名 | 类型 | 主键 | 注释 |
| :--- | :--- | :--- | :--- |
| `package_prefix` | `VARCHAR(255)` | ✅ | SDK的关键包名前缀，例如 `com.tencent.bugly` |
| `sdk_name` | `VARCHAR(255)` | | SDK的正式名称，例如 "腾讯Bugly" |
| `category` | `VARCHAR(100)` | | SDK分类，例如 "Crash Reporting" |
| `description` | `TEXT` | | SDK的功能描述 |
| `status` | `VARCHAR(50)` | | 记录状态: `identified`, `pending_analysis`, `error` |
| `last_checked_at` | `DATETIME` | | 上次检查或更新的时间 |

### 2.3 应用版本与SDK关联表 (`class_app_version_sdks`)

最终结果表，记录哪个应用版本包含了哪个SDK。

**表结构 (`class_app_version_sdks`):**

| 字段名 | 类型 | 外键 | 注释 |
| :--- | :--- | :--- | :--- |
| `app_version_id` | `INTEGER` | `app_version.id` | 关联到具体的APK版本 |
| `sdk_package_prefix` | `VARCHAR(255)` | `class_sdk_knowledge_base.package_prefix` | 关联到具体的SDK |
| `created_at` | `DATETIME` | | 创建时间 |
| `updated_at` | `DATETIME` | | 更新时间 |

---

## 第三阶段：自动化工作流

### 3.1 `sdk_matching_flow` (SDK匹配与关联流程)

这是一个全新的、极致高效的匹配流程，它完全在数据库层面工作，实现了自动回溯。

**核心逻辑**：
该流程被定时触发（例如每小时一次），执行一个高效的SQL查询，一次性找出所有"应该存在但尚未存在"的匹配关系，并批量插入 `class_app_version_sdks` 表。

**SQL伪代码示例**：
```sql
INSERT INTO class_app_version_sdks (app_version_id, sdk_package_prefix)
SELECT DISTINCT
    adp.app_version_id,
    skb.package_prefix
FROM
    class_app_discovered_packages adp
JOIN
    class_sdk_knowledge_base skb ON adp.package_name LIKE skb.package_prefix || '%'
WHERE
    skb.status = 'identified'
    AND NOT EXISTS (
        SELECT 1
        FROM class_app_version_sdks avs
        WHERE avs.app_version_id = adp.app_version_id
          AND avs.sdk_package_prefix = skb.package_prefix
    );
```

**优势**：
-   **自动回溯**：当 `class_sdk_knowledge_base` 新增一个SDK（例如 `com.new.sdk`）后，下一次流程运行时，上述查询会自动将这个新SDK与历史上所有包含 `com.new.sdk.*` 包名的应用版本关联起来。
-   **极致效率**：所有匹配逻辑都下沉到数据库执行，避免了在应用层进行大量的循环和查询，性能极高。
-   **幂等性**：`NOT EXISTS` 子句确保了每次只插入新的关联关系，重复运行不会产生副作用。

### 3.2 `knowledge_base_enrichment_flow` (知识库增强流程)

这个后台流程负责让知识库"成长"。

1.  **发现未知包**：定期查询，找出那些出现在 `class_app_discovered_packages` 中，但其任何前缀都未在 `class_sdk_knowledge_base` 中出现的包名。
2.  **提交分析**：将这些"未知包"以 `pending_analysis` 状态插入 `class_sdk_knowledge_base`。
3.  **LLM分析**：调用大模型（如Gemini）分析这些 `pending_analysis` 的包名，并结构化地返回其信息（是否为SDK、名称、分类、描述等）。
4.  **更新知识库**：将LLM的分析结果更新回 `class_sdk_knowledge_base`，将状态标记为 `identified`。

### 3.3 Prefect UI 配置参数

所有流程在 Prefect UI 中都可以配置相关参数：

-   **`apk_analysis_flow`**:
    -   `batch_size`: 每次处理的APK数量，默认1000
    -   `enable_artifact`: 是否生成执行报告，默认为true

-   **`sdk_matching_flow`**:
    -   `enable_artifact`: 是否生成执行报告，默认为true

-   **`knowledge_base_enrichment_flow`**:
    -   `max_pending_analysis`: 每次处理的最大待分析包名数量，默认100
    -   `enable_artifact`: 是否生成执行报告，默认为true

### 3.4 Artifact 输出

所有流程都会生成详细的执行报告Artifact，包括：
-   流程执行时间统计
-   处理的数据量统计
-   成功/失败记录数
-   性能指标
-   错误详情（如果有）

---

## 第四阶段：配置管理

### 4.1 黑名单配置

SDK包名黑名单配置文件位于 `tenyy/src/config/sdk_blacklist.conf`，支持以下格式：

```conf
# 系统与标准库包名
android.*
androidx.*
com.android.*

java.*
javax.*
kotlin.*
kotlinx.*
dalvik.*

org.apache.*
org.json
org.xml.*
org.w3c.*

# Google通用库
com.google.gson
com.google.guava
com.google.protobuf
com.google.common.*

# 测试框架
junit.*
org.junit.*
org.hamcrest.*
com.squareup.leakcanary
```

该配置文件支持动态更新，无需重启服务即可生效。

### 4.2 流程参数配置

所有流程参数均可在Prefect UI中配置，支持以下配置项：
-   批处理大小
-   是否启用artifact输出
-   日志级别
-   其他流程特定参数

---

## 总结

该V2.0方案通过 **流程解耦** 和 **数据驱动** 的思想，构建了一个健壮、高效且具备自学习能力的SDK识别系统。

-   ✅ **架构清晰**：数据采集、知识库增强、数据匹配三个流程各司其职，易于维护和扩展。
-   ✅ **自动回溯**：知识库的任何更新都能自动惠及所有历史数据，无需手动回扫。
-   ✅ **性能卓越**：核心匹配逻辑下沉至数据库，避免了应用层的性能瓶颈。
-   ✅ **数据优质**：通过"智能过滤"环节，从源头保证了数据的准确性，降低了后续处理的复杂性。
-   ✅ **灵活配置**：支持在Prefect UI中灵活配置参数，适应不同运行环境。
-   ✅ **可视化监控**：通过Artifact输出详细的执行报告，方便监控和调试。