# coding: utf-8
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Index
from tenyy.src.models.base import Base


class class_AppDiscoveredPackage(Base):
    """应用发现包表，存储从APK中提取并提纯后的候选包名"""
    __tablename__ = 'class_app_discovered_packages'
    __table_args__ = (
        Index('idx_app_discovered_packages_app_version_id', 'app_version_id'),
        Index('idx_app_discovered_packages_package_name', 'package_name'),
        Index('idx_app_discovered_packages_last_checked', 'last_checked')
    )

    id = Column(BigInteger, primary_key=True)  # 自增ID
    app_version_id = Column(Integer, nullable=False)  # 外键，关联到 app_version.id
    package_name = Column(String(255), nullable=False)  # 提纯后的候选包名
    last_checked = Column(DateTime, nullable=True)  # 最后检查时间，用于增量更新

    def __repr__(self):
        return f"<class_AppDiscoveredPackage(id={self.id}, app_version_id={self.app_version_id}, package_name='{self.package_name}')>"
