from sqlalchemy import inspect
from sqlalchemy.engine import Row

# 从base.py导入Base类，确保所有模型使用相同的Base类
from tenyy.src.models.base import Base as DeclarativeBase

class Base(DeclarativeBase):
    __abstract__ = True

    @classmethod
    def to_dict(cls, models):
        if isinstance(models, list):
            if len(models) == 0:
                return []
            if isinstance(models[0], Row):
                return [row._asdict() for row in models]
            else:
                return [cls._asdict(model) for model in models]
        else:
            if not models:
                return {}
            if isinstance(models, Row):
                return models._asdict()
            return cls._asdict(models)

    def _asdict(self):
        return {c.key: getattr(self, c.key) for c in inspect(self).mapper.column_attrs}

# 导入所有模型类，确保它们在元数据中注册
from tenyy.src.models.app import App
from tenyy.src.models.store_app import StoreApp
from tenyy.src.models.app_version import AppVersion
# 导入新增的模型类
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage
from tenyy.src.models.class_sdk_knowledge_base import class_SDKKnowledgeBase
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK