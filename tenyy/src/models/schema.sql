-- Table: app
CREATE TABLE app (
    id VARCHAR PRIMARY KEY,
    name TEXT,
    description TEXT,
    category TEXT,
    is_game BOOLEA<PERSON>,
    developer TEXT,
    icon TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    metadata JSONB
);

-- Table: store_app
CREATE TABLE store_app (
    id SERIAL PRIMARY KEY,
    app_id VARCHAR NOT NULL,
    CONSTRAINT fk_app
        FOREIGN KEY (app_id)
        REFERENCES app (id),
    store_type VARCHAR NOT NULL,
    store_id VARCHAR,
    store_name TEXT
);

-- Table: app_version
CREATE TABLE app_version (
    id SERIAL PRIMARY KEY,
    store_app_id INTEGER NOT NULL,
    CONSTRAINT fk_store_app
        FOREIGN KEY (store_app_id)
        REFERENCES store_app (id),
    version VARCHAR,
    apk_hash VARCHAR,
    download_url TEXT,
    apk_size BIGINT,
    release_date TIMESTAMP WITHOUT TIME ZONE,
    min_sdk INTEGER,
    target_sdk INTEGER,
    permissions JSONB,
    features <PERSON><PERSON><PERSON>B,
    libraries JSONB,
    analyzed_at TIMESTAMP WITHOUT TIME ZONE,
    analysis_result JSONB,
    is_latest BOOLEAN,
    description TEXT,
    editor_intro VARCHAR,
    tags VARCHAR,
    snap_shots TEXT,
    permissions_list JSONB,
    download_num BIGINT,
    icp_number VARCHAR,
    icp_entity VARCHAR,
    detail_json JSONB,
    store_name TEXT,
    store_description TEXT,
    icon_url TEXT,
    rating NUMERIC,
    review_count INTEGER,
    download_count BIGINT,
    price NUMERIC,
    is_free BOOLEAN,
    last_updated TIMESTAMP WITHOUT TIME ZONE,
    developer TEXT,
    operator TEXT,
    username TEXT,
    category TEXT,
    tag_alias TEXT,
    game_type TEXT,
    is_game BOOLEAN,
    cp_id TEXT,
    ms_store_id TEXT,
    ms_store_status TEXT,
    ios_url TEXT,
    privacy_agreement TEXT,
    booking_url TEXT,
    exe_download_url TEXT,
    video TEXT,
    is_cloud_game BOOLEAN,
    is_pc_yyb_available BOOLEAN,
    is_booking BOOLEAN,
    restrict_level TEXT,
    syzs_download_num TEXT,
    booking_user_cnt TEXT,
    public_time TEXT,
    app_update_time TEXT,
    channel_info TEXT,
    show_text TEXT,
    detail_template TEXT,
    ios_app_link_info TEXT,
    booking_gift TEXT,
    cloud_game_info JSONB,
    store_metadata JSONB,

    -- 下载相关字段
    filename TEXT,  -- 下载后的文件路径（MinIO路径）目前不需要了
    download_status VARCHAR,  -- 下载状态：pending, downloading, downloaded, failed
    last_attempt TIMESTAMP WITHOUT TIME ZONE,  -- 最后尝试下载时间
    error_message TEXT,  -- 下载失败时的错误信息
    retry_count INTEGER DEFAULT 0,  -- 重试次数

    -- 分析相关字段
    analyze_status VARCHAR,  -- 分析状态：pending, analyzing, done, failed
    analyze_error_message TEXT,  -- 分析失败时的错误信息

    -- APK分析结果字段
    android_manifest TEXT,  -- AndroidManifest.xml内容
    packages_class JSONB,  -- 包名信息列表（去重排序后的包名）
    lib_files JSONB  -- so库文件列表（去重排序后的库文件名）
);

-- Table: class_app_discovered_packages
CREATE TABLE class_app_discovered_packages (
    id BIGSERIAL PRIMARY KEY,
    app_version_id INTEGER NOT NULL,
    package_name VARCHAR(255) NOT NULL,
    last_checked TIMESTAMP WITHOUT TIME ZONE
);

CREATE INDEX idx_app_discovered_packages_app_version_id ON class_app_discovered_packages (app_version_id);
CREATE INDEX idx_app_discovered_packages_package_name ON class_app_discovered_packages (package_name);
CREATE INDEX idx_app_discovered_packages_last_checked ON class_app_discovered_packages (last_checked);

-- Table: class_sdk_knowledge_base
CREATE TABLE class_sdk_knowledge_base (
    package_prefix VARCHAR(255) PRIMARY KEY,
    sdk_name VARCHAR(255),
    category VARCHAR(100),
    description TEXT,
    status VARCHAR(50),
    last_checked_at TIMESTAMP WITHOUT TIME ZONE
);

CREATE INDEX idx_sdk_knowledge_base_package_prefix ON class_sdk_knowledge_base (package_prefix);

-- Table: class_app_version_sdks
CREATE TABLE class_app_version_sdks (
    app_version_id INTEGER NOT NULL,
    sdk_package_prefix VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    PRIMARY KEY (app_version_id, sdk_package_prefix)
);

CREATE INDEX idx_app_version_sdks_app_version_id ON class_app_version_sdks (app_version_id);
CREATE INDEX idx_app_version_sdks_sdk_package_prefix ON class_app_version_sdks (sdk_package_prefix);