# coding: utf-8
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql.sqltypes import Numeric, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from tenyy.src.models.base import Base
from tenyy.src.models.app import App

class StoreApp(Base):
    """商店应用表，存储不同商店的特定信息"""
    __tablename__ = 'store_app'

    id = Column(Integer, primary_key=True)
    app_id = Column(String, ForeignKey('app.id'), nullable=False)  # 关联主表
    store_type = Column(String, nullable=False)  # 商店类型(huawei/yyb等)
    store_id = Column(String)  # 商店中的ID
    store_name = Column(Text)  # 商店中的名称

    # 定义与主表的关系
    app = relationship("App", back_populates="store_apps")
    
    # 定义与版本表的关系
    versions = relationship("AppVersion", back_populates="store_app")
