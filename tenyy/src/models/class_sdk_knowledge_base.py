# coding: utf-8
from sqlalchemy import Column, String, Text, DateTime, Index
from tenyy.src.models.base import Base


class class_SDKKnowledgeBase(Base):
    """SDK知识库表，存储所有已识别的SDK信息"""
    __tablename__ = 'class_sdk_knowledge_base'
    __table_args__ = (
        Index('idx_sdk_knowledge_base_package_prefix', 'package_prefix'),
    )

    package_prefix = Column(String(255), primary_key=True)  # SDK的关键包名前缀
    sdk_name = Column(String(255), nullable=True)  # SDK的正式名称
    category = Column(String(100), nullable=True)  # SDK分类
    description = Column(Text, nullable=True)  # SDK的功能描述
    status = Column(String(50), nullable=True)  # 记录状态: identified, pending_analysis, error
    last_checked_at = Column(DateTime, nullable=True)  # 上次检查或更新的时间

    def __repr__(self):
        return f"<class_SDKKnowledgeBase(package_prefix='{self.package_prefix}', sdk_name='{self.sdk_name}', status='{self.status}')>"
