# coding: utf-8
from sqlalchemy import Column, Integer, String, DateTime, Index
from tenyy.src.models.base import Base


class class_AppVersionSDK(Base):
    """应用版本与SDK关联表，记录哪个应用版本包含了哪个SDK"""
    __tablename__ = 'class_app_version_sdks'
    __table_args__ = (
        Index('idx_app_version_sdks_app_version_id', 'app_version_id'),
        Index('idx_app_version_sdks_sdk_package_prefix', 'sdk_package_prefix')
    )

    app_version_id = Column(Integer, primary_key=True)  # 关联到具体的APK版本
    sdk_package_prefix = Column(String(255), primary_key=True)  # 关联到具体的SDK
    created_at = Column(DateTime, nullable=True)  # 创建时间
    updated_at = Column(DateTime, nullable=True)  # 更新时间

    def __repr__(self):
        return f"<class_AppVersionSDK(app_version_id={self.app_version_id}, sdk_package_prefix='{self.sdk_package_prefix}')>"