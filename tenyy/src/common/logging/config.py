"""日志配置"""
import os
import sys
from pathlib import Path

def setup_logging():
    """配置日志系统
    
    根据环境配置不同的日志输出:
    
    本地开发环境:
    1. 控制台彩色输出(stderr) - 用于开发调试
    2. JSON文件输出 - 用于分析
    3. 标准日志文件 - 用于查看
    
    Docker环境:
    1. JSON格式输出到stdout - 被Docker日志驱动收集
    2. 错误日志文件 - 双写保证关键错误可查
    """
    from loguru import logger  # 延迟导入避免循环依赖
    
    # 清除默认配置
    logger.remove()
    
    # 检测运行环境
    is_docker = os.getenv('DOCKER_ENV', 'false').lower() == 'true'
    hostname = os.getenv('HOSTNAME', 'unknown')
    service_name = os.getenv('SERVICE_NAME', 'unknown')
    
    if is_docker:
        # Docker环境: 输出到stdout，JSON格式
        logger.add(
            sys.stdout,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            level="INFO",
            serialize=True,   # JSON格式
            backtrace=True,   # 异常堆栈
            diagnose=True,    # 变量值
            enqueue=True      # 异步写入
        )
        # 为日志添加容器信息
        logger = logger.bind(
            container=hostname,
            service=service_name
        )
    else:
        # 本地开发环境: 控制台彩色输出
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
            backtrace=True,   # 显示完整异常栈
            diagnose=True     # 显示变量值
        )

    def setup_log_file(filepath: Path, mode: int = 0o666) -> None:
        """设置日志文件权限
        
        Args:
            filepath: 日志文件路径
            mode: 权限模式，默认666 (-rw-rw-rw-)
        """
        if filepath.exists():
            try:
                os.chmod(filepath, mode)
                logger.debug("设置文件权限",
                           path=str(filepath),
                           mode=oct(mode)[-3:])
            except Exception as e:
                logger.warning(f"无法设置文件权限: {str(e)}",
                           path=str(filepath),
                           error=str(e))
        else:
            logger.warning(f"日志文件不存在",
                         path=str(filepath))
    
    try:
        # 确定日志目录
        log_dir = Path('/app/logs' if is_docker else 'logs')
        log_dir.mkdir(parents=True, exist_ok=True)
        os.chmod(log_dir, 0o777)  # 设置目录权限为777
        
        logger.debug("日志目录初始化成功",
                    log_dir=str(log_dir),
                    permissions=oct(os.stat(log_dir).st_mode)[-3:])

        if is_docker:
            # Docker环境只保留错误日志文件
            logger.add(
                log_dir / "error.log",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
                rotation="100 MB",
                retention="10 days",
                compression="zip",
                level="ERROR",
                backtrace=True,
                diagnose=True,
                enqueue=True,
                catch=True
            )
        else:
            # 本地环境使用完整的文件日志
            log_files = ["app.json", "app.log", "error.log"]
            for filename in log_files:
                filepath = log_dir / filename
                filepath.touch(exist_ok=True)  # 创建文件
                setup_log_file(filepath)  # 设置权限
            
            # JSON文件输出
            logger.add(
                log_dir / "app.json",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
                serialize=True,  # 输出JSON格式
                rotation="100 MB",    # 单个文件最大100MB
                retention="10 days",  # 保留10天
                compression="zip",    # 压缩旧日志
                level="INFO",
                enqueue=True,    # 异步写入
                catch=True       # 捕获写入错误
            )
        
            # 标准日志文件
            logger.add(
                log_dir / "app.log",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
                rotation="100 MB",
                retention="10 days",
                compression="zip",
                level="INFO",
                enqueue=True,
                catch=True
            )
            
            # 配置异常处理
            logger.add(
                log_dir / "error.log",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
                rotation="100 MB",
                retention="10 days",
                compression="zip",
                level="ERROR",
                backtrace=True,
                diagnose=True,
                enqueue=True,
                catch=True
            )
        
    except Exception as e:
        logger.warning(f"无法配置文件日志，将只使用控制台输出: {str(e)}",
                      error_type=type(e).__name__,
                      error=str(e))
