# tenyy/src/common/data_writer.py

import os
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session as DbSession
from typing import List, Dict, Any, Union, Optional
from contextlib import contextmanager
import json

from tenyy.src.models.app import App
from tenyy.src.models.store_app import StoreApp
from tenyy.src.models.app_version import AppVersion

from tenyy.src.config.settings import settings
from tenyy.src.common.logging import get_logger

logger = get_logger("common.data_writer")

# Global, process-safe placeholders for the engine and session factory.
engine = None
SessionLocal = None

def get_engine():
    """Lazily initializes and returns a process-safe SQLAlchemy engine."""
    global engine
    if engine is None:
        try:
            logger.debug("Initializing new SQLAlchemy engine for this process...")
            engine = create_engine(
                f"postgresql://{settings.postgres_user}:{settings.postgres_password}@"
                f"{settings.postgres_host}:{settings.postgres_port}/{settings.postgres_db}"
            )
        except Exception as e:
            logger.error(f"Database engine creation failed: {e}", exc_info=True)
            raise
    return engine

def get_session_local():
    """Lazily initializes and returns a process-safe session factory."""
    global SessionLocal
    if SessionLocal is None:
        try:
            logger.debug("Initializing new SessionLocal factory for this process...")
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=get_engine())
        except Exception as e:
            logger.error(f"Session factory creation failed: {e}", exc_info=True)
            raise
    return SessionLocal

@contextmanager
def get_db_session():
    """Provides a transactional scope around a series of operations using a lazily-initialized, process-safe session."""
    try:
        session_factory = get_session_local()
        db = session_factory()
        yield db
    except Exception as e:
        logger.error(f"An error occurred within the DB session scope: {e}", exc_info=True)
        # Re-raise the exception to ensure the calling task knows about the failure.
        raise
    finally:
        if 'db' in locals() and db.is_active:
            db.close()

def save_app_data(app_data: Dict[str, Any], store_type: str) -> Optional[Dict[str, bool]]:
    """
    Saves standardized data for a single app to the database.
    This function is transactional; it either succeeds completely or rolls back.

    :param app_data: A standardized dictionary containing all app information.
    :param store_type: The identifier for the app store (e.g., 'yinyongbao', 'huawei').
    """
    with get_db_session() as session:
        try:
            pkg_name = app_data.get('pkg_name')
            md5 = app_data.get('md5')

            stats = {
                'new_app': False,
                'new_store_app': False,
                'new_version': False
            }

            if not pkg_name or not md5:
                logger.warning(f"Skipping invalid data: missing pkg_name or md5. Data: {app_data}")
                return None

            # 1. Check if the version already exists, and skip if it does.
            existing_version = session.query(AppVersion).filter(AppVersion.apk_hash == md5).first()
            if existing_version:
                logger.info(f"[Version Exists] {pkg_name} -> APK Hash {md5}")
                return None

            # 2. Handle the main App record (create if it doesn't exist).
            existing_app = session.query(App).filter(App.id == pkg_name).first()
            if not existing_app:
                new_app = App(
                    id=pkg_name,
                    name=app_data.get('name'),
                    description=app_data.get('description'),
                    category=app_data.get('category'),
                    is_game=app_data.get('is_game', False),
                    developer=app_data.get('developer'),
                    icon=app_data.get('icon'),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(new_app)
                stats['new_app'] = True
                logger.info(f"[New App] Created main record for: {pkg_name}")

            # 3. Handle the StoreApp record (create if it doesn't exist).
            store_app = session.query(StoreApp).filter(
                StoreApp.app_id == pkg_name,
                StoreApp.store_type == store_type
            ).first()

            if not store_app:
                store_app = StoreApp(
                    app_id=pkg_name,
                    store_type=store_type,
                    store_id=app_data.get('store_id'),
                    store_name=app_data.get('name')
                )
                session.add(store_app)
                stats['new_store_app'] = True
                logger.info(f"[New Store App] Created store record for: {pkg_name} from {store_type}")
            
            session.flush()



            # For TEXT columns, convert dicts/lists to JSON strings. For JSONB, pass the object directly.
            for key in ['channel_info', 'ios_app_link_info', 'booking_gift', 'detail_template']:
                value = app_data.get(key)
                if isinstance(value, (dict, list)):
                    app_data[key] = json.dumps(value, ensure_ascii=False)


            # 4. Create the AppVersion record.
            new_version = AppVersion(
                apk_hash=md5,
                store_app=store_app,
                version=str(app_data.get('version_name', '')),
                download_url=app_data.get('download_url'),
                apk_size=app_data.get('file_size'),
                release_date=app_data.get('release_date'),  # 添加 release_date 字段
                description=app_data.get('version_description'),
                editor_intro=app_data.get('editor_intro'),
                tags=app_data.get('tags'),
                snap_shots=app_data.get('snap_shots'),
                permissions_list=app_data.get('permissions_list'),
                store_metadata=app_data.get('store_metadata'),
                detail_json=app_data.get('detail_json'),
                download_num=app_data.get('download_num'),
                download_count=app_data.get('download_count'),
                icp_number=app_data.get('icp_number'),
                icp_entity=app_data.get('icp_entity'),
                operator=app_data.get('operator'),
                privacy_agreement=app_data.get('privacy_agreement'),
                video=app_data.get('video'),
                is_cloud_game=app_data.get('is_cloud_game'),
                is_pc_yyb_available=app_data.get('is_pc_yyb_available'),
                is_booking=app_data.get('is_booking'),
                restrict_level=app_data.get('restrict_level'),
                syzs_download_num=app_data.get('syzs_download_num'),
                booking_user_cnt=app_data.get('booking_user_cnt'),
                public_time=app_data.get('public_time'),
                app_update_time=app_data.get('app_update_time'),
                channel_info=app_data.get('channel_info'),
                show_text=app_data.get('show_text'),
                detail_template=app_data.get('detail_template'),
                ios_app_link_info=app_data.get('ios_app_link_info'),
                ios_url=app_data.get('ios_url'),
                booking_gift=app_data.get('booking_gift'),
                cloud_game_info=app_data.get('cloud_game_info'),
                rating=app_data.get('rating'),
                last_updated=app_data.get('last_updated'),
                developer=app_data.get('developer'),
                category=app_data.get('category'),
                is_game=app_data.get('is_game'),
                icon_url=app_data.get('icon'),
                store_name=app_data.get('name'),
                store_description=app_data.get('version_description'),
                username=app_data.get('username'),
                tag_alias=app_data.get('tag_alias'),
                game_type=app_data.get('game_type'),
                cp_id=app_data.get('cp_id'),
                ms_store_id=app_data.get('ms_store_id'),
                ms_store_status=app_data.get('ms_store_status'),
                booking_url=app_data.get('booking_url'),
                exe_download_url=app_data.get('exe_download_url'),
            )
            session.add(new_version)
            stats['new_version'] = True
            logger.info(f"[New Version] Created version record: {pkg_name} v{app_data.get('version_name')}")

            session.commit()
            logger.info(f"Successfully saved app data for: {pkg_name} v{app_data.get('version_name')}")
            return stats

        except Exception as e:
            logger.error(f"An error occurred while saving app data for {pkg_name}: {e}", exc_info=True)
            session.rollback()
            raise
