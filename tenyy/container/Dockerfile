# Tenyy 统一 Dockerfile - 支持 crawler 和 download_extract 两个模块
# 使用多阶段构建优化镜像体积和构建速度
# 使用构建参数 ENV_TYPE 来区分环境配置：local 或 production
# 优化缓存策略：分层构建，最大化缓存利用率

ARG ENV_TYPE=local

# ============================================================================
# 🏗️ 构建阶段 - 安装依赖和编译
# ============================================================================
FROM python:3.11-slim AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖（仅在构建阶段需要）
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 构建工具 (download_extract APK分析需要)
    build-essential \
    gcc \
    g++ \
    # Python开发依赖
    python3-dev \
    # Git (某些pip包需要)
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件 (单独复制以最大化缓存利用)
COPY requirements.txt .

# 创建虚拟环境并安装依赖 (分层缓存优化)
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 升级pip (单独层，避免频繁重建)
RUN pip install --no-cache-dir --upgrade pip

# 安装依赖 (单独层，只有requirements.txt变更时才重建)
RUN pip install --no-cache-dir -r requirements.txt

# ============================================================================
# 🚀 运行阶段 - 最小化运行时镜像
# ============================================================================
FROM python:3.11-slim AS runtime

# 设置工作目录
WORKDIR /app

# 仅安装运行时必需的系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 基础工具
    curl \
    unzip \
    # 网络工具 (健康检查和数据库连接检查需要)
    netcat-openbsd \
    # PostgreSQL 客户端工具 (数据库备份需要)
    postgresql-client \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
# 确保虚拟环境的Python优先级最高
ENV PATH="/opt/venv/bin:$PATH"
ENV VIRTUAL_ENV="/opt/venv"

# 创建必要的目录 (download_extract 需要)
RUN mkdir -p /tmp/apk_downloads \
    && mkdir -p /app/logs \
    && chmod 755 /tmp/apk_downloads

# 分层复制文件以最大化缓存利用率
# 第1层：requirements文件 (变更频率最低)
COPY requirements*.txt ./

# 第2层：配置文件 (变更频率低)
COPY tenyy/config/ ./tenyy/config/

# 第3层：Prefect配置 (变更频率中等)
COPY prefect.yaml ./

# 第4层：构建脚本 (变更频率中等)
COPY Makefile generate_prefect_config.py ./

# 第5层：应用代码 (变更频率最高，放在最后)
COPY tenyy/src/ ./tenyy/src/

# 第6层：其他脚本文件
COPY tenyy/container/Dockerfile ./tenyy/container/

# 设置环境变量
ENV PYTHONPATH=/app
ENV PATH=/usr/local/bin:$PATH
ENV PYTHONUNBUFFERED=1

# 创建统一的启动脚本（支持 crawler 和 download_extract 模块）
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 显示启动信息\n\
echo "🚀 Tenyy 统一容器启动..."\n\
echo "📦 环境: ${TENYY_ENV:-auto-detect}"\n\
echo "🐳 容器: ${HOSTNAME:-unknown}"\n\
echo "⏰ 时间: $(date)"\n\
\n\
# 等待数据库连接（如果需要）\n\
if [ -n "$POSTGRES_HOST" ] || [ -n "$DB_HOST" ]; then\n\
    DB_HOST_TO_CHECK="${POSTGRES_HOST:-${DB_HOST}}"\n\
    DB_PORT_TO_CHECK="${POSTGRES_PORT:-${DB_PORT:-5432}}"\n\
    echo "⏳ 等待数据库连接 ${DB_HOST_TO_CHECK}:${DB_PORT_TO_CHECK}..."\n\
    \n\
    # 最多等待60秒\n\
    TIMEOUT=60\n\
    ELAPSED=0\n\
    while ! nc -z ${DB_HOST_TO_CHECK} ${DB_PORT_TO_CHECK}; do\n\
        if [ $ELAPSED -ge $TIMEOUT ]; then\n\
            echo "❌ 数据库连接超时 (${TIMEOUT}s)"\n\
            exit 1\n\
        fi\n\
        echo "⏳ 等待数据库 ${DB_HOST_TO_CHECK}:${DB_PORT_TO_CHECK}... (${ELAPSED}s)"\n\
        sleep 2\n\
        ELAPSED=$((ELAPSED + 2))\n\
    done\n\
    echo "✅ 数据库连接成功!"\n\
fi\n\
\n\
# 强制重置环境变量，确保使用容器内的虚拟环境\n\
export PATH="/opt/venv/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"\n\
export VIRTUAL_ENV="/opt/venv"\n\
export PYTHONPATH=/app\n\
\n\
# 显示执行的命令\n\
echo "🔧 执行命令: $@"\n\
\n\
# 执行传入的命令\n\
exec "$@"' > /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from tenyy.config import validate_config; exit(0 if validate_config() else 1)" || exit 1

# 统一的入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令 - 显示配置信息
CMD ["python", "-c", "from tenyy.config import print_config_summary; print_config_summary(); print('✅ 容器就绪，等待 Prefect 任务执行...')"]
