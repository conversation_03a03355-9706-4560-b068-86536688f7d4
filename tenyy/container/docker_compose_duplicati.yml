version: '3.8'

services:
  duplicati_app:
    image: duplicati/duplicati:latest
    container_name: duplicati_app
    ports:
      - "8200:8200"cd
    volumes:
      - ./data:/data
      - /mnt/ssd/tenyy/db:/data/db
      - /mnt/ssd/tenyy/db_backups:/data/db_backups
      - /mnt/ssd/tenyy/duplicati_config:/config
    environment:
      - SETTINGS_ENCRYPTION_KEY=zhangdi168
      - DUPLICATI__WEBSERVICE_PASSWORD=zhangdi168
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - duplicati-network

networks:
  duplicati-network:
    driver: bridge