# Docker Swarm 下载服务配置 (aria2-service & aria2-ui)
#
# 该文件包含独立的下载服务 stack，用于处理 APK 下载任务
# 与主服务分离以实现更好的故障隔离和资源管理
#
version: '3.8'

# 全局日志配置 - 应用于所有服务
x-logging: &default-logging
  driver: json-file
  options:
    max-size: "10m"
    max-file: "3"

services:
  # Aria2 下载服务
  aria2-service:
    image: p3terx/aria2-pro
    environment:
      - RPC_SECRET=${ARIA2_RPC_TOKEN:-zhangdi168}
      - RPC_PORT=6800
      - LISTEN_PORT=6888
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Shanghai
      - MAX_CONCURRENT_DOWNLOADS=25
      - DISK_CACHE=256M
      - ENABLE_RPC=true
      - RPC_ALLOW_ORIGIN_ALL=true
      - RPC_LISTEN_ALL=true
      # 禁用 BT 相关功能
      - ENABLE_BITTORRENT=false
      - ENABLE_METALINK=false
    ports:
      - "6800:6800"
      - "6888:6888"
    volumes:
      - aria2_downloads:/downloads
      - aria2_config:/config
    networks:
      - download-net
    dns:
      - 8.8.8.8
      - 114.114.114.114
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == worker
      resources:
        limits:
          memory: 18G
          cpus: '2.0'
        reservations:
          memory: 4G
          cpus: '1.0'
      # 严格的更新策略，确保先停止旧实例
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: stop-first
      rollback_config:
        parallelism: 1
        delay: 30s
        order: stop-first
      restart_policy:
        condition: any  # 任何情况下都重启，确保服务持续在线
        delay: 10s
        max_attempts: 0  # 无限制重启尝试
        window: 120s
    # 健康检查确保服务正常运行，但使用更宽松的配置
    healthcheck:
      test: ["CMD-SHELL", "curl -s -o /dev/null -w \"%{http_code}\" http://localhost:6800/jsonrpc | grep -q \"400\\|200\" || exit 1"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 90s
    logging: *default-logging  # 应用全局日志配置

  # Aria2 Web UI (可选) - 使用纯UI镜像，避免与aria2-service冲突
  aria2-ui:
    image: p3terx/ariang
    command: ["--port", "6880", "--ipv6"]
    ports:
      - "6880:6880"
    networks:
      - download-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == worker
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'
    depends_on:
      - aria2-service
    logging: *default-logging  # 应用全局日志配置

volumes:
  # Aria2相关数据卷
  aria2_downloads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/downloads
  aria2_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/aria2_config

networks:
  download-net:
    driver: overlay
    attachable: true

# ============================================================================
# 🚀 部署说明
# ============================================================================

# 部署前准备工作:
# 1. 在所有worker节点上创建数据目录(使用SSD主磁盘):
#    sudo mkdir -p /mnt/ssd/tenyy/{downloads,aria2_config}
#    sudo chown -R 1000:1000 /mnt/ssd/tenyy
#    sudo chmod -R 755 /mnt/ssd/tenyy
#
# 2. 设置环境变量
#    export ARIA2_RPC_TOKEN=zhangdi168
#
# 3. 部署命令:
#    docker stack deploy -c docker-swarm-download.yml download
#
# 4. 验证部署:
#    docker service ls
#    docker service logs download_aria2-service
#
# ============================================================================
# 🔧 服务访问端点
# ============================================================================
# - Aria2 RPC: http://<host>:6800/jsonrpc
# - Aria2 Web UI: http://<host>:6880
# ============================================================================