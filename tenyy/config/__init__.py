# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - 配置加载器和统一接口

提供统一的配置加载接口，根据环境变量自动选择对应的配置文件。
支持配置优先级：环境变量 > 环境配置文件 > 全局配置文件 > 代码默认值
"""

import os
import sys
from typing import Dict, Any, Optional
from pathlib import Path

# ============================================================================
# 🔧 环境检测和配置加载
# ============================================================================

def get_environment() -> str:
    """
    获取当前运行环境
    
    优先级：
    1. 环境变量 TENYY_ENV
    2. 环境变量 ENVIRONMENT  
    3. 根据运行上下文自动检测
    4. 默认为 local
    """
    # 优先使用显式设置的环境变量
    env = os.getenv("TENYY_ENV") or os.getenv("ENVIRONMENT")
    if env:
        return env.lower()
    
    # 自动检测环境
    if os.getenv("DOCKER_ENV") == "true":
        # 在Docker容器中运行
        if "192.168.1.101" in os.getenv("PREFECT_API_URL", ""):
            return "production"
        else:
            return "prefect_local_container"
    else:
        # 在宿主机上运行
        return "local"

def load_config(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    加载指定环境的配置
    
    Args:
        environment: 环境名称，如果不指定则自动检测
        
    Returns:
        配置字典
        
    Raises:
        ValueError: 不支持的环境类型
        ImportError: 配置模块加载失败
    """
    if environment is None:
        environment = get_environment()
    
    environment = environment.lower()
    
    try:
        if environment == "local":
            from .local import CONFIG
        elif environment == "prefect_local_container":
            from .prefect_local_container import CONFIG
        elif environment == "production":
            from .production import CONFIG
        else:
            raise ValueError(f"不支持的环境类型: {environment}")
        
        print(f"✅ 已加载 {environment} 环境配置")
        return CONFIG
        
    except ImportError as e:
        raise ImportError(f"加载 {environment} 环境配置失败: {e}")

# ============================================================================
# 🌍 全局配置实例
# ============================================================================

# 自动加载当前环境配置
current_environment = get_environment()
config = load_config(current_environment)

# 导出常用配置项，方便直接导入使用
DATABASE_CONFIG = config["database"]
PREFECT_CONFIG = config["prefect"]
ARIA2_CONFIG = config["aria2"]
DOCKER_CONFIG = config["docker"]
NETWORK_CONFIG = config["network"]
STORAGE_CONFIG = config["storage"]
CRAWLER_CONFIG = config["crawler"]
DOWNLOAD_EXTRACT_CONFIG = config["download_extract"]

# 导出环境信息
ENVIRONMENT = current_environment
ENVIRONMENT_NAME = config.get("environment_name", current_environment)

# ============================================================================
# 🔧 配置工具函数
# ============================================================================

def get_database_url(async_driver: bool = False) -> str:
    """
    生成数据库连接URL
    
    Args:
        async_driver: 是否使用异步驱动
        
    Returns:
        数据库连接URL
    """
    db_config = DATABASE_CONFIG
    driver = "postgresql+asyncpg" if async_driver else "postgresql"
    
    return (
        f"{driver}://{db_config['username']}:{db_config['password']}"
        f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    )

def get_prefect_api_url() -> str:
    """获取Prefect API URL"""
    return PREFECT_CONFIG["api_url"]

def get_docker_image() -> str:
    """获取完整的Docker镜像名称"""
    return DOCKER_CONFIG["full_image"]

def get_aria2_config() -> Dict[str, Any]:
    """获取Aria2配置"""
    return ARIA2_CONFIG.copy()

def is_production() -> bool:
    """判断是否为生产环境"""
    return ENVIRONMENT == "production"

def is_container_env() -> bool:
    """判断是否在容器环境中运行"""
    return ENVIRONMENT in ["prefect_local_container", "production"]

def get_work_pool_name() -> str:
    """获取Prefect工作池名称"""
    return PREFECT_CONFIG["work_pool_name"]

def get_log_level() -> str:
    """获取日志级别"""
    return PREFECT_CONFIG.get("logging_level", "INFO")

# ============================================================================
# 🔍 配置调试和验证
# ============================================================================

def print_config_summary():
    """打印配置摘要，用于调试"""
    print(f"""
🔧 Tenyy 配置摘要
==================
环境: {ENVIRONMENT_NAME} ({ENVIRONMENT})
数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}
Prefect: {PREFECT_CONFIG['api_url']}
Docker镜像: {DOCKER_CONFIG['full_image']}
Aria2: {ARIA2_CONFIG['rpc_url']}
工作池: {PREFECT_CONFIG['work_pool_name']}
==================
    """)

def validate_config() -> bool:
    """验证当前配置的完整性"""
    try:
        # 检查必要的配置项
        required_keys = ["database", "prefect", "aria2", "docker", "network"]
        for key in required_keys:
            if key not in config:
                print(f"❌ 缺少配置项: {key}")
                return False
        
        # 检查数据库配置
        db_required = ["host", "port", "database", "username", "password"]
        for key in db_required:
            if key not in DATABASE_CONFIG:
                print(f"❌ 缺少数据库配置项: {key}")
                return False
        
        # 检查Prefect配置
        if not PREFECT_CONFIG.get("api_url"):
            print("❌ 缺少Prefect API URL")
            return False
        
        print("✅ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

# ============================================================================
# 🚀 模块初始化
# ============================================================================

# 在模块加载时进行配置验证
if not validate_config():
    print("⚠️  配置验证失败，但继续加载")

# 如果是调试模式，打印配置摘要
if os.getenv("TENYY_DEBUG") == "true":
    print_config_summary()

# ============================================================================
# 🔧 向后兼容性支持
# ============================================================================

# 为了兼容现有代码，提供一些常用的配置别名 (统一使用POSTGRES_*格式)
POSTGRES_HOST = DATABASE_CONFIG["host"]
POSTGRES_PORT = DATABASE_CONFIG["port"]
POSTGRES_DB = DATABASE_CONFIG["database"]
POSTGRES_USER = DATABASE_CONFIG["username"]
POSTGRES_PASSWORD = DATABASE_CONFIG["password"]

PREFECT_API_URL = PREFECT_CONFIG["api_url"]
WORK_POOL_NAME = PREFECT_CONFIG["work_pool_name"]

ARIA2_RPC_URL = ARIA2_CONFIG["rpc_url"]
ARIA2_RPC_TOKEN = ARIA2_CONFIG["rpc_token"]
DOWNLOAD_DIR = ARIA2_CONFIG["download_dir"]

# ============================================================================
# 🔧 导出列表
# ============================================================================

__all__ = [
    # 主要配置对象
    "config",
    "DATABASE_CONFIG",
    "PREFECT_CONFIG", 
    "ARIA2_CONFIG",
    "DOCKER_CONFIG",
    "NETWORK_CONFIG",
    "STORAGE_CONFIG",
    "CRAWLER_CONFIG",
    "DOWNLOAD_EXTRACT_CONFIG",
    
    # 环境信息
    "ENVIRONMENT",
    "ENVIRONMENT_NAME",
    
    # 工具函数
    "get_database_url",
    "get_prefect_api_url",
    "get_docker_image",
    "get_aria2_config",
    "is_production",
    "is_container_env",
    "get_work_pool_name",
    "get_log_level",
    
    # 调试函数
    "print_config_summary",
    "validate_config",
    
    # 向后兼容 (统一使用POSTGRES_*格式)
    "POSTGRES_HOST", "POSTGRES_PORT", "POSTGRES_DB", "POSTGRES_USER", "POSTGRES_PASSWORD",
    "PREFECT_API_URL", "WORK_POOL_NAME",
    "ARIA2_RPC_URL", "ARIA2_RPC_TOKEN", "DOWNLOAD_DIR",

    # 新增统一配置接口
    "get_config",
]

# ============================================================================
# 🔧 统一配置接口
# ============================================================================

def get_config(environment: Optional[str] = None):
    """
    获取指定环境的配置对象

    Args:
        environment: 环境名称，如果不指定则自动检测

    Returns:
        配置模块对象，包含所有配置属性
    """
    if environment is None:
        environment = get_environment()

    # 导入对应环境的配置模块
    if environment == "local":
        from . import local as config_module
    elif environment == "prefect_local_container":
        from . import prefect_local_container as config_module
    elif environment == "production":
        from . import production as config_module
    else:
        raise ValueError(f"不支持的环境: {environment}")

    return config_module
