# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - 全局基础配置

包含所有模块共享的配置项，不包含环境特定的配置。
这些配置在本地、容器、生产环境中都是相同的。
"""

from typing import Dict, Any, List, Optional
from pathlib import Path

# ============================================================================
# 🗄️ 数据库表和字段配置 (所有环境通用)
# ============================================================================

# 数据库表名
TABLE_NAMES = {
    "app_version": "app_version",
}

# 状态字段值定义
STATUS_VALUES = {
    "download_status": {
        "pending": "pending",
        "downloading": "downloading", 
        "downloaded": "downloaded",
        "failed": "failed",
    },
    "analyze_status": {
        "pending": "pending",
        "processing": "processing",
        "completed": "completed", 
        "failed": "failed",
    }
}

# 需要填充的分析结果字段
ANALYSIS_FIELDS = [
    "android_manifest",  # AndroidManifest.xml内容
    "packages_class",    # 包名列表（JSONB）
    "lib_files",         # so库文件列表（JSONB）
]

# ============================================================================
# 📝 日志配置 (所有环境通用)
# ============================================================================

LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# ============================================================================
# 🔄 重试和错误处理配置 (所有环境通用)
# ============================================================================

RETRY_CONFIG = {
    "max_retries": 2,
    "retry_delay": 60,  # 秒
    "exponential_backoff": True,
    "max_retry_delay": 300,  # 秒
}

# ============================================================================
# 📦 APK处理配置 (所有环境通用)
# ============================================================================

PROCESSING_CONFIG = {
    "max_concurrent_downloads": 100,
    "download_timeout": 600,  # 秒
    "analysis_timeout": 300,  # 秒
    "cleanup_temp_files": True,
    "temp_file_prefix": "apk_temp_",
}

# ============================================================================
# 🕷️ 爬虫通用配置 (所有环境通用)
# ============================================================================

# HTTP请求通用配置
HTTP_CONFIG = {
    "timeout": 20,
    "max_retries": 3,
    "retry_delay": 1,
    "pool_connections": 30,
    "pool_maxsize": 50,
}

# 爬取通用配置
CRAWL_CONFIG = {
    "page_delay_range": [1, 3],  # 页面间延迟范围（秒）
    "enable_detail_crawl": True,
    "use_internal_process_pool": True,
    "internal_pool_workers": 10,
    "max_pages": -1,  # -1表示无限制
}

# ============================================================================
# 🔧 Prefect工作池配置 (所有环境通用)
# ============================================================================

# 统一工作池名称
WORK_POOL_NAME = "tenyy-unified-pool"

# Docker作业通用配置
DOCKER_JOB_CONFIG = {
    "image_pull_policy": "Always",
    "auto_remove": True,
    "restart_policy": "Always",
}

# 资源限制配置
RESOURCE_LIMITS = {
    "crawler": {
        "cpu_limit": "2.0",
        "memory_limit": "2Gi",
    },
    "download_extract": {
        "scheduler": {
            "cpu_limit": "1.0", 
            "memory_limit": "1Gi",
        },
        "processor": {
            "cpu_limit": "4.0",
            "memory_limit": "4Gi", 
        }
    }
}

# ============================================================================
# 📁 存储路径配置 (所有环境通用)
# ============================================================================

STORAGE_CONFIG = {
    "download_temp_path": "/tmp/storage/temp",
    "log_file_path": "/tmp/tenyy.log",
    "download_dir": "/downloads",
    "temp_apk_dir": "/tmp/apk_downloads",
}

# ============================================================================
# 🔧 Aria2通用配置 (所有环境通用)
# ============================================================================

ARIA2_COMMON_CONFIG = {
    "max_connections": 25,
    "split": 25,
    "timeout": 300,
    "retry_wait": 5,
    "max_retries": 3,
}

# ============================================================================
# 📊 性能和并发配置 (所有环境通用)
# ============================================================================

SCHEDULER_CONFIG = {
    "target_concurrency": 300,
    "check_interval": 60,  # 秒
    "batch_size": 10,
    "max_batch_size": 50,
}

# ============================================================================
# 🏷️ 爬虫类型配置 (所有环境通用)
# ============================================================================

CRAWLER_TYPES = {
    "yinyongbao": {
        "app": {
            "description": "应用宝应用商店爬虫",
            "tags": ["crawler", "yinyongbao", "app"],
        },
        "game": {
            "description": "应用宝游戏商店爬虫", 
            "tags": ["crawler", "yinyongbao", "game"],
        },
        "similar": {
            "description": "应用宝推荐应用爬虫",
            "tags": ["crawler", "yinyongbao", "similar"],
        }
    },
    "huawei": {
        "app": {
            "description": "华为应用商店爬虫",
            "tags": ["crawler", "huawei", "app"],
        }
    }
}

# ============================================================================
# 🐳 Docker镜像配置 (所有环境通用)
# ============================================================================

DOCKER_CONFIG = {
    "image_name": "tenyy-unified",
    "image_tag": "latest",
    "image": "tenyy-unified:latest",  # 完整镜像名称
    "dockerfile": "tenyy/container/Dockerfile",
    "pythonpath": "/app",
    "working_dir": "/app",
}

# ============================================================================
# 🚀 资源配置 (所有环境通用)
# ============================================================================

# 容器资源配置
RESOURCE_CONFIG = {
    "crawler": {
        "cpu_request": "1.0",
        "memory_request": "2Gi",
        "cpu_limit": "0",  # 0表示无限制
        "memory_limit": "0",  # 0表示无限制
    },
    "download": {
        "cpu_request": "1.0",
        "memory_request": "2Gi",
        "cpu_limit": "0",  # 0表示无限制，自适应分配
        "memory_limit": "0",  # 0表示无限制，自适应分配
    },
    "continuous_download": {
        "cpu_request": "2.0",  # 循环处理需要更多资源
        "memory_request": "4Gi",
        "cpu_limit": "0",  # 0表示无限制，自适应分配
        "memory_limit": "0",  # 0表示无限制，自适应分配
    }
}

# ============================================================================
# 🔧 配置验证函数
# ============================================================================

def validate_base_config() -> bool:
    """验证基础配置的完整性"""
    required_configs = [
        TABLE_NAMES,
        STATUS_VALUES,
        LOGGING_CONFIG,
        RETRY_CONFIG,
        PROCESSING_CONFIG,
        HTTP_CONFIG,
        CRAWL_CONFIG,
    ]
    
    return all(config for config in required_configs)

# 在模块加载时验证配置
if not validate_base_config():
    raise ValueError("基础配置验证失败，请检查配置完整性")
