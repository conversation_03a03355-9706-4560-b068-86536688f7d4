# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - 生产环境配置

生产集群环境特定的配置，使用集群地址和生产级配置。
适用于Docker Swarm集群部署的生产环境。
"""

import os
from .base import *  # 导入所有基础配置

# ============================================================================
# 🌍 环境标识
# ============================================================================

ENVIRONMENT = "production"
ENVIRONMENT_NAME = "生产集群环境"

# ============================================================================
# 🗄️ 数据库配置 - 生产环境
# ============================================================================

DATABASE_CONFIG = {
    "host": "app-db",  # 生产环境固定值
    "port": 5432,      # 生产环境固定值
    "database": "tenyy_app",  # 生产环境固定值
    "username": "admin",      # 生产环境固定值
    "password": "zhangdi168", # 生产环境固定密码
}

# 统一数据库环境变量配置 (仅使用POSTGRES_*格式)
DB_CONFIG = {
    "POSTGRES_HOST": DATABASE_CONFIG["host"],
    "POSTGRES_PORT": str(DATABASE_CONFIG["port"]),
    "POSTGRES_DB": DATABASE_CONFIG["database"],
    "POSTGRES_USER": DATABASE_CONFIG["username"],
    "POSTGRES_PASSWORD": DATABASE_CONFIG["password"],
}

# ============================================================================
# 🔄 Prefect配置 - 生产环境
# ============================================================================

PREFECT_CONFIG = {
    "api_url": os.getenv("PREFECT_API_URL", "http://*************:4200/api"),
    "ui_url": "http://*************:4200",
    "work_pool_name": WORK_POOL_NAME,
    "logging_level": "INFO",
}

# ============================================================================
# 📥 Aria2配置 - 生产环境
# ============================================================================

ARIA2_CONFIG = {
    "rpc_url": "http://*************:6800/jsonrpc",  # 生产环境Aria2服务地址
    "rpc_token": "zhangdi168",  # 生产环境令牌
    "download_dir": "/downloads",  # 容器内下载目录
    **ARIA2_COMMON_CONFIG  # 继承通用配置
}

# ============================================================================
# 🐳 Docker配置 - 生产环境
# ============================================================================

DOCKER_REGISTRY = {
    "host": "*************",
    "port": "5000",
    "url": "*************:5000",
}



DOCKER_IMAGE_CONFIG = {
    "registry": DOCKER_REGISTRY["url"],
    "image_name": DOCKER_CONFIG["image_name"],
    "tag": DOCKER_CONFIG["image_tag"],
    "full_image": f"{DOCKER_REGISTRY['url']}/{DOCKER_CONFIG['image_name']}:{DOCKER_CONFIG['image_tag']}",
}

# ============================================================================
# 🌐 网络配置 - 生产环境
# ============================================================================

NETWORK_CONFIG = {
    "docker_network": "tenyy-crawler-stack_tenyy-net",  # Swarm overlay网络
    "network_mode": "tenyy-crawler-stack_tenyy-net",
}

# ============================================================================
# 📁 存储路径配置 - 生产环境
# ============================================================================

PRODUCTION_STORAGE_CONFIG = {
    **STORAGE_CONFIG,  # 继承基础存储配置
    "log_file_path": "/var/log/tenyy_production.log",
    "host_download_dir": "/data/downloads",  # 生产环境下载目录
}

# ============================================================================
# 🔧 Prefect部署配置 - 生产环境
# ============================================================================

PREFECT_DEPLOYMENT_CONFIG = {
    "work_pool": {
        "name": WORK_POOL_NAME,
        "type": "docker",
    },
    "job_variables": {
        "image": DOCKER_IMAGE_CONFIG["full_image"],
        "networks": [NETWORK_CONFIG["docker_network"]],  # 使用Swarm网络
        "env": {
            "PREFECT_LOGGING_LEVEL": "INFO",
            "PYTHONPATH": DOCKER_CONFIG["pythonpath"],
            "DOCKER_ENV": "true",
            "ENVIRONMENT": "production",
            **DB_CONFIG,  # 数据库环境变量
            **{
                "ARIA2_RPC_URL": ARIA2_CONFIG["rpc_url"],
                "ARIA2_RPC_TOKEN": ARIA2_CONFIG["rpc_token"],
                "DOWNLOAD_DIR": ARIA2_CONFIG["download_dir"],
            }
        },
        "volumes": [
            f"{PRODUCTION_STORAGE_CONFIG['host_download_dir']}:{ARIA2_CONFIG['download_dir']}",
            f"/data/apk_temp:{PRODUCTION_STORAGE_CONFIG['temp_apk_dir']}",
        ],
        **RESOURCE_LIMITS["crawler"],  # 使用爬虫资源限制作为默认
    }
}

# ============================================================================
# 🕷️ 爬虫特定配置 - 生产环境
# ============================================================================

CRAWLER_CONFIG = {
    "config_dir": "tenyy/src/crawler/configs",
    "yinyongbao_config": "tenyy/src/crawler/configs/yinyongbao.yaml",
    "huawei_config": "tenyy/src/crawler/configs/huawei.yaml",
    "categories_dir": "tenyy/src/crawler",
}

# ============================================================================
# 📦 Download-Extract特定配置 - 生产环境
# ============================================================================

DOWNLOAD_EXTRACT_CONFIG = {
    "scheduler_deployment": {
        "name": "adaptive-scheduler-prod",
        "description": "自适应调度器 - 生产环境",
        "work_queue": "scheduler-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["scheduler"],
        }
    },
    "processor_deployment": {
        "name": "apk-processor-prod",
        "description": "APK处理器 - 生产环境",
        "work_queue": "processor-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["processor"],
        }
    }
}

# ============================================================================
# 🔧 环境验证函数
# ============================================================================

def validate_production_config() -> bool:
    """验证生产环境配置"""
    required_configs = [
        DATABASE_CONFIG,
        PREFECT_CONFIG,
        ARIA2_CONFIG,
        DOCKER_REGISTRY,
        NETWORK_CONFIG,
    ]
    
    # 检查必要的配置项
    for config in required_configs:
        if not config:
            return False
    
    # 检查生产环境特定配置
    if "*************" not in PREFECT_CONFIG["api_url"]:
        print("警告: 生产环境Prefect API地址不正确")
        return False
    
    if DATABASE_CONFIG["password"] == "your_strong_password_here":
        print("警告: 生产环境使用默认密码，建议更改")
        # 不返回False，允许使用默认密码进行测试
    
    return True

# 在模块加载时验证配置
if not validate_production_config():
    raise ValueError("生产环境配置验证失败，请检查配置")

# ============================================================================
# 🚀 Prefect部署配置 - 生产环境
# ============================================================================

# 生产环境Prefect部署配置
HOST_DOWNLOAD_DIR = "/mnt/ssd/tenyy/downloads"
DOCKER_IMAGE = "*************:5000/tenyy-unified:latest"
DOCKER_NETWORK = "tenyy-stack_tenyy-net"
PREFECT_API_URL = "http://*************:4200/api"

# ============================================================================
# 🔧 配置导出
# ============================================================================

# 导出给其他模块使用的配置字典
CONFIG = {
    "environment": ENVIRONMENT,
    "database": DATABASE_CONFIG,
    "prefect": PREFECT_CONFIG,
    "aria2": ARIA2_CONFIG,
    "docker": DOCKER_IMAGE_CONFIG,
    "network": NETWORK_CONFIG,
    "storage": PRODUCTION_STORAGE_CONFIG,
    "crawler": CRAWLER_CONFIG,
    "download_extract": DOWNLOAD_EXTRACT_CONFIG,
}
