"""日志模块"""
from loguru import logger as _logger
from .config import setup_logging

# 初始化日志配置并获取logger实例
setup_logging()

def get_logger(name: str = None):
    """获取配置好的logger实例
    
    Args:
        name: 模块名称，用于标识日志来源
        
    Returns:
        配置好的logger实例
    
    Example:
        >>> logger = get_logger("downloader")
        >>> logger.info("开始下载")
        >>> 
        >>> @logger.catch
        >>> def download():
        >>>     ...
    """
    if name:
        return _logger.bind(name=name)
    return _logger

# 为了向后兼容，保留原始logger
logger = get_logger()
