#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
BaseCrawler - 通用爬虫基类

提供了爬虫的基础框架，包括：
- 配置管理
- HTTP客户端
- 数据映射
- 通用流程控制
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from prefect import get_run_logger
from .config_manager import ConfigManager
from .http_client import HttpClient
from .data_mapper import DataMapper
from .utils import DataCleaner


class BaseCrawler(ABC):
    """爬虫基类，定义了通用的爬虫接口和流程"""
    
    def __init__(self, config_name: str):
        """
        初始化爬虫

        Args:
            config_name: 配置文件名（不含扩展名），如 'huawei', 'yinyongbao'
        """
        self._logger = None
        self.config = ConfigManager.load_config(config_name)
        self.http_client = HttpClient(self.config.get('http', {}))
        self.data_mapper = DataMapper(config_name)
        self.data_cleaner = DataCleaner()
        
        # 从配置中获取基本信息
        self.store_type = self.config.get('store_type', config_name)
        self.base_url = self.config.get('base_url')
        self.headers = self.config.get('http', {}).get('default_headers', {})

    @property
    def logger(self):
        """延迟初始化logger"""
        if self._logger is None:
            try:
                from prefect import get_run_logger
                self._logger = get_run_logger()
            except:
                # 如果没有Prefect上下文，使用标准logging
                import logging
                self._logger = logging.getLogger(__name__)
        return self._logger
        
    @abstractmethod
    def get_categories(self) -> List[Dict[str, Any]]:
        """
        获取所有分类信息
        
        Returns:
            分类列表，每个分类包含必要的标识信息
        """
        pass
    
    @abstractmethod
    def scrape_list_page(self, category: Dict[str, Any], page_num: int) -> Tuple[List[Dict[str, Any]], bool]:
        """
        爬取指定分类和页码的应用列表
        
        Args:
            category: 分类信息
            page_num: 页码
            
        Returns:
            (应用列表, 是否有下一页)
        """
        pass
    
    @abstractmethod
    def scrape_detail_page(self, app_id: str) -> Optional[Dict[str, Any]]:
        """
        爬取应用详情页
        
        Args:
            app_id: 应用ID
            
        Returns:
            应用详情数据，如果失败返回None
        """
        pass
    
    def process_and_standardize(self, raw_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        处理和标准化原始数据
        
        Args:
            raw_data: 原始数据
            **kwargs: 额外参数（如is_game等）
            
        Returns:
            标准化后的数据
        """
        return self.data_mapper.map_to_standard(raw_data, **kwargs)
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """
        验证数据完整性
        
        Args:
            data: 待验证的数据
            
        Returns:
            是否通过验证
        """
        required_fields = ['pkg_name', 'name', 'store_id']
        for field in required_fields:
            if not data.get(field):
                self.logger.warning(f"Missing required field: {field}")
                return False
        return True
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
