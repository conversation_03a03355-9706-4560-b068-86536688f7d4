# coding: utf-8
from sqlalchemy import Column, String, Text, DateTime
from sqlalchemy.sql.sqltypes import Numeric, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from tenyy.src.models.base import Base

class App(Base):
    """应用主表，以Bundle ID(pkg_name)为主键"""
    __tablename__ = 'app'

    id = Column(String, primary_key=True)  # Bundle ID
    name = Column(Text)  # 通用应用名称
    description = Column(Text)  # 通用描述
    category = Column(Text)  # 应用分类
    is_game = Column(Boolean)  # 是否游戏
    developer = Column(Text)  # 开发者
    icon = Column(Text)  # 图标URL
    created_at = Column(DateTime)  # 创建时间
    updated_at = Column(DateTime)  # 更新时间
    metadata_ = Column('metadata', JSONB)  # 扩展元数据
    
    # 定义关系
    store_apps = relationship("StoreApp", back_populates="app")
