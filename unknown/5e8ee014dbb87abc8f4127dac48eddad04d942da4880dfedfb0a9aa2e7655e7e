# Huawei App Store 爬虫配置

store_type: "huawei"
base_url: "https://store-drcn.hispace.dbankcloud.cn/hwmarket/api/clientApi"

# HTTP配置
http:
  timeout: 20
  max_retries: 3
  retry_delay: 1
  pool_connections: 15  # 增加连接池以避免警告
  pool_maxsize: 30
  default_headers:
    accept: "application/json"
    user-agent: "HiSpace##13.4.1.301##samsung##SM-S9210"
    sysuseragent: "Mozilla/5.0 (Linux; Android 9; SM-S9210 Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36"
    content-encoding: "gzip"
    content-type: "application/x-gzip"
    accept-encoding: "identity"
  user_agents:
    - "HiSpace##13.4.1.301##samsung##SM-S9210"
    - "HiSpace##13.4.1.301##huawei##P40"
    - "HiSpace##13.4.1.301##xiaomi##Mi11"

# 分类配置文件路径
categories:
  app_file: "tenyy/src/crawler/huaweiCat/huaweiapp.json"
  game_file: "tenyy/src/crawler/huaweiCat/huaweigame.json"

# 请求配置
request_config:
  # 基础请求体模板
  base_payload:
    method: "internal.getTabDetail"
    serviceType: 20
    reqPageNum: 1
    maxResults: 25
    version: "10.0.0"
    zone: "CN"
    locale: "zh"

# 数据映射配置
data_mapping:
  # 字段映射规则
  field_mapping:
    pkg_name: "package"
    name: "name"
    store_id: "detailId"
    icon: "icon"
    md5: "md5"
    version_name: "versionName"
    download_url: "downurl"
    file_size: "size"
    
  # 需要从详情页提取的字段
  detail_fields:
    - "developer"
    - "category"
    - "rating"
    - "app_intro"
    - "store_description_memo"
    - "last_updated"

# 爬取配置
crawl_config:
  # 页面间延迟（秒）
  page_delay_range: [1, 3]
  # 是否启用详情页爬取
  enable_detail_crawl: true

  # 容器内进程池配置（新增）
  use_internal_process_pool: true
  internal_pool_workers: 10
