# ============================================================================
# 🚫 Git和版本控制
# ============================================================================
.git/
.gitignore
.gitattributes

# ============================================================================
# 🐍 Python相关
# ============================================================================
.venv/
venv/
env/
**/__pycache__/
**/*.pyc
**/*.pyo
**/*.pyd
*.egg-info/
tenyy.egg-info/
.pytest_cache/
.coverage
.tox/

# ============================================================================
# 📁 存储和数据目录 (重要：排除大文件)
# ============================================================================
storage/
tenyy/storage/
logs/
*.log
postgres-data/
redis-data/

# APK下载目录 (重要：排除大文件，避免镜像体积过大)
aria2-downloads/
**/aria2-downloads/
tenyy/container/local/local_download/
**/local_download/
apk_temp/
**/apk_temp/
*.apk
**/*.apk

# ============================================================================
# 🔧 开发和配置文件
# ============================================================================
.env
.env.*
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================================================
# 📊 监控和日志系统
# ============================================================================
loki-data/
loki-rules/
grafana-data/
signoz/
victorialogs-data/
promtail-data/

# ============================================================================
# 🐳 Docker相关
# ============================================================================
Dockerfile.dev
docker-compose.override.yml
.dockerignore.bak

# ============================================================================
# 🚀 构建缓存优化文件
# ============================================================================
.last-requirements-hash
.last-src-hash
.last-dockerfile-hash
build-smart.sh
build-optimized.sh

# ============================================================================
# �📦 构建产物和临时文件
# ============================================================================
dist/
build/
*.tar.gz
*.zip
temp/
tmp/

# ============================================================================
# 📝 文档和说明文件 (保留重要的配置文档)
# ============================================================================
README.dev.md
CHANGELOG.md
docs/
*.md
!file_config.md
!README.md