"""add three new class_tables

Revision ID: 3814cfba8c5f
Revises: 
Create Date: 2025-08-03 07:25:21.851527

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3814cfba8c5f'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('class_app_discovered_packages',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('app_version_id', sa.Integer(), nullable=False),
    sa.Column('package_name', sa.String(length=255), nullable=False),
    sa.Column('last_checked', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_app_discovered_packages_app_version_id', 'class_app_discovered_packages', ['app_version_id'], unique=False)
    op.create_index('idx_app_discovered_packages_last_checked', 'class_app_discovered_packages', ['last_checked'], unique=False)
    op.create_index('idx_app_discovered_packages_package_name', 'class_app_discovered_packages', ['package_name'], unique=False)
    op.create_table('class_app_version_sdks',
    sa.Column('app_version_id', sa.Integer(), nullable=False),
    sa.Column('sdk_package_prefix', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('app_version_id', 'sdk_package_prefix')
    )
    op.create_index('idx_app_version_sdks_app_version_id', 'class_app_version_sdks', ['app_version_id'], unique=False)
    op.create_index('idx_app_version_sdks_sdk_package_prefix', 'class_app_version_sdks', ['sdk_package_prefix'], unique=False)
    op.create_table('class_sdk_knowledge_base',
    sa.Column('package_prefix', sa.String(length=255), nullable=False),
    sa.Column('sdk_name', sa.String(length=255), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('last_checked_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('package_prefix')
    )
    op.create_index('idx_sdk_knowledge_base_package_prefix', 'class_sdk_knowledge_base', ['package_prefix'], unique=False)
    op.create_foreign_key(None, 'app_version', 'store_app', ['store_app_id'], ['id'])
    op.create_foreign_key(None, 'store_app', 'app', ['app_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'store_app', type_='foreignkey')
    op.drop_constraint(None, 'app_version', type_='foreignkey')
    op.drop_index('idx_sdk_knowledge_base_package_prefix', table_name='class_sdk_knowledge_base')
    op.drop_table('class_sdk_knowledge_base')
    op.drop_index('idx_app_version_sdks_sdk_package_prefix', table_name='class_app_version_sdks')
    op.drop_index('idx_app_version_sdks_app_version_id', table_name='class_app_version_sdks')
    op.drop_table('class_app_version_sdks')
    op.drop_index('idx_app_discovered_packages_package_name', table_name='class_app_discovered_packages')
    op.drop_index('idx_app_discovered_packages_last_checked', table_name='class_app_discovered_packages')
    op.drop_index('idx_app_discovered_packages_app_version_id', table_name='class_app_discovered_packages')
    op.drop_table('class_app_discovered_packages')
    # ### end Alembic commands ###
