#!/usr/bin/env python3
"""
获取Prefect API URL的工具脚本
用于Makefile中动态获取配置，避免配置加载时的输出信息
"""

import sys
import os

def get_prefect_api_url(env_name):
    """获取指定环境的Prefect API URL"""
    try:
        # 添加项目路径
        sys.path.insert(0, '.')
        sys.path.insert(0, './tenyy')
        
        # 临时禁用输出
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        # 重定向输出到null
        with open(os.devnull, 'w') as devnull:
            sys.stdout = devnull
            sys.stderr = devnull
            
            try:
                from tenyy.config import get_config
                config = get_config(env_name)
                api_url = config.PREFECT_API_URL
            finally:
                # 恢复输出
                sys.stdout = original_stdout
                sys.stderr = original_stderr
        
        return api_url
        
    except Exception:
        # 降级到默认配置
        if env_name == "production":
            return "http://*************:4200/api"
        else:
            return "http://localhost:4200/api"

if __name__ == "__main__":
    env_name = sys.argv[1] if len(sys.argv) > 1 else "local"
    api_url = get_prefect_api_url(env_name)
    print(api_url, end='')  # 不输出换行符
