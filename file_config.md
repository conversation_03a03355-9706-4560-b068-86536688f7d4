# 文件配置说明

## `generate_prefect_config.py`

此脚本用于动态生成 `prefect.yaml` 配置文件。它根据环境变量（如 `production`、`local`）从 `tenyy/config` 目录下的统一配置中读取数据库密码、镜像地址、网络设置等信息，并填充到 `prefect.yaml` 的模板中。

### 主要功能：

1.  **环境隔离**：通过传入不同的环境名称（如 `python generate_prefect_config.py production`），可以为不同环境生成专属的配置文件，确保开发、测试和生产环境的隔离。
2.  **集中化配置**：所有环境相关的配置项都集中在 `tenyy/config` 中管理，避免了在 `prefect.yaml` 中硬编码敏感信息和易变配置。
3.  **自动化生成**：简化了部署流程，每次更新配置后，只需运行此脚本即可生成最新的 `prefect.yaml`，减少了手动修改的错误。

### 任务容器配置摘要：

-   **爬虫任务容器 (`yinyongbao-app-crawler`, `yinyongbao-game-crawler`, etc.)**
    -   **CPU 核心**：限制为 `1.0` 个核心。
    -   **内存**：限制为 `2GiB`。
    -   **网络**：连接到由环境配置指定的 Docker 网络。
    -   **重启策略**：始终重启 (`Always`)。

-   **下载与分析任务容器 (`pipeline-apk-processing`)**
    -   **CPU 核心**：
        -   **请求 (request)**：`2.0` (至少保证2个核心)。
        -   **限制 (limit)**：`0` (无硬性上限，可使用节点所有可用CPU)。
    -   **内存**：
        -   **预留 (reservation)**：`4GiB` (至少保证4GB可用)。
        -   **限制 (limit)**：`16GiB` (最多可使用16GB)。
    -   **挂载卷**：将宿主机的下载目录 (例如 `/mnt/ssd/tenyy/downloads`) 挂载到容器的 `/downloads` 目录，实现文件共享。

## `prefect-worker` 与任务容器的关系

-   `prefect-worker` 是一个常驻服务，负责监听 Prefect Server 的指令。
-   当一个工作流（如 `pipeline-apk-processing`）被触发时，`prefect-worker` 会根据 `prefect.yaml` 中定义的 `job_variables`（如镜像、网络、资源限制等）动态地**创建一个新的、独立的任务容器**。
-   这个新创建的任务容器才是真正执行工作流代码（如 `continuous_pipeline_apk_processing_flow` 函数）的实体。
- 任务容器执行完毕后会自动销毁，而 `prefect-worker` 会继续监听下一个任务，实现了资源的按需分配和弹性伸缩。

## 变更记录

### 2024-12-19 APK分析线程池优化 🎯
**问题**: 线程池资源浪费 - 每个APK创建8线程但只用1线程

**核心问题分析**:
- **资源浪费**: 每个APK分析创建`os.cpu_count()`个线程，但只使用1个
- **过度设计**: 单个APK分析本身就是单线程操作，无需多线程池
- **内存浪费**: 10个APK同时处理时创建80个线程，实际只用10个

**修改内容**:
- **文件**: `tenyy/src/download_extract/flows/apk_processor.py`
- **修改**: `ThreadPoolExecutor(max_workers=os.cpu_count())` → `ThreadPoolExecutor(max_workers=1)`
- **原理**: 每个APK分析只需要1个线程，精确匹配资源需求

**性能优化效果**:
- **线程数量**: 从80个线程降低到10个线程（10个APK同时处理）
- **内存使用**: 大幅减少线程创建和维护的内存开销
- **资源利用**: 从87.5%资源浪费降低到0%浪费
- **系统稳定性**: 减少线程竞争，提高系统稳定性

**技术原理**:
```python
# 优化前：过度设计
with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:  # 8线程
    await loop.run_in_executor(executor, analyze_apk_sync, path)  # 只用1线程

# 优化后：精确匹配
with ThreadPoolExecutor(max_workers=1) as executor:  # 1线程
    await loop.run_in_executor(executor, analyze_apk_sync, path)  # 用满1线程
```

**设计思路**: 简单直接，每个APK分析对应1个线程，避免过度工程化

### 2024-12-19 APK分析线程池并行化重大修改 🚀
**问题**: "单CPU分析导致其他全部等待"的性能瓶颈

**核心问题分析**:
1. **单线程阻塞**: APK分析直接在主线程中执行同步的`androguard`库操作
2. **伪并发**: 虽然使用了`asyncio.gather`，但CPU密集型操作阻塞事件循环
3. **性能损失**: 10个工作者协程退化为单工作者模式，理论性能损失87.5%
4. **资源浪费**: 多核CPU未被充分利用

**修改内容**:
- **文件**: `tenyy/src/download_extract/flows/apk_processor.py`
- **新增**: `analyze_apk_sync()` 同步分析函数，专门用于线程池执行
- **修改**: `APKAnalyzer.analyze_apk()` 方法，使用`ThreadPoolExecutor`实现真正并行
- **导入**: 添加`from concurrent.futures import ThreadPoolExecutor`

**技术实现**:
```python
# 使用线程池执行CPU密集型的APK分析
with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
    success, analysis_result, error_msg = await loop.run_in_executor(
        executor, analyze_apk_sync, apk_file_path
    )
```

**性能提升预期**:
- **并发模式**: 从"单CPU串行"改为"多线程并行"
- **CPU利用率**: 从单核提升到多核利用
- **处理时间**: 大幅缩短总体处理时间
- **吞吐量**: 显著提高APK处理吞吐量

**选择线程池而非进程池的原因**:
1. **内存效率**: 线程共享内存空间，内存使用更高效
2. **启动速度**: 线程创建和切换比进程更快
3. **通信简单**: 线程间通信比进程间通信更简单
4. **GIL考虑**: `androguard`包含C扩展，可以释放GIL实现真正并行

**文档**: 创建了`apk_thread_pool_modification.md`详细说明修改内容和技术原理

### 2024-12-19 Aria2配置冲突问题分析
**问题**: AriaNG在10个并发下载任务下无法连接

**根本原因**:
1. **并发下载数配置冲突**:
   - Docker环境: `MAX_CONCURRENT_DOWNLOADS=25`
   - Python配置: `max_concurrent_downloads=100` 
   - 实际代码: `max-concurrent-downloads=1`

2. **连接数配置冲突**:
   - 本地环境: `MAX_CONNECTION_PER_SERVER=16`
   - 代码配置: `max-connection-per-server=25`

3. **资源瓶颈**:
   - 10个并发任务 × 25个连接 = 250个并发连接
   - 超出aria2c全局连接数限制(通常100-200)
   - 内存和CPU压力过大

### 2024-12-19 分片大小性能影响分析
**问题**: 分片配置不一致且分片过小影响性能

**当前配置冲突**:
- `apk_processor.py`: `split=8`, `min-split-size=1M`
- `base.py`: `split=25`  
- `config.py`: `split=10`

**分片过小的性能问题**:
1. **HTTP请求开销**: 100MB文件÷1MB分片=100个请求，纯网络延迟6秒+
2. **I/O碎片化**: 频繁小块写入导致磁盘碎片，性能下降10倍
3. **内存开销**: 25个分片需要25MB+225KB开销，比4个分片多3倍内存
4. **并发复杂度**: 过度并发导致CPU浪费在线程调度上

**最佳分片配置建议**:
- APK文件(50-200MB): `split=4`, `min-split-size=20M`
- 预期性能提升: 60-80%，内存节省50%
- 动态策略: 根据文件大小自动调整分片数(2-8个)

- **`tenyy/container/docker-swarm-compose.yml`**: 修复了 `aria2-ui` 无法连接 `aria2-service` 的问题。通过移除 `network_mode: host` 并将 `aria2-service` 加入 `tenyy-net` 网络，统一了服务间的通信网络。
- **`tenyy/container/docker-swarm-compose.yml`**: 调整了 `aria2-service` 的资源限制，将其内存上限从 `8G` 调整为 `4G`，预留从 `1G` 调整为 `2G`，以防止因内存溢出导致的频繁重启，提高服务稳定性。
- **`tenyy/container/docker-swarm-compose.yml`**: 根据用户需求，为 `aria2-service` 提供了更强的内存保障。移除了内存上限（`limits`），并将内存预留（`reservations`）提高到 `4G`，以最大限度避免因内存不足导致的重启。

## Aria2 配置冲突问题分析 (2024-12-19)

### 发现的严重配置冲突

**问题背景**: 在 10 个并发 APK 下载任务场景下，除了轮询频率问题外，发现多个严重的 aria2 配置冲突可能导致 AriaNg 无法连接。

#### 1. 并发下载数配置冲突 ⚠️
- **Docker 环境** (`docker-swarm-compose.yml`): `MAX_CONCURRENT_DOWNLOADS=25`
- **Python 配置** (`config.py`): `max_concurrent_downloads=100`  
- **实际代码** (`apk_processor.py`): `"max-concurrent-downloads": "1"`
- **影响**: 配置不一致导致 aria2c 行为不可预测

#### 2. 连接数配置冲突 ⚠️
- **本地环境** (`docker-local-aria2-shared.yml`): `MAX_CONNECTION_PER_SERVER=16`
- **代码中** (`apk_processor.py`): `"max-connection-per-server": "25"`
- **影响**: 代码要求 25 个连接但 aria2c 只允许 16 个，导致连接失败

#### 3. 资源瓶颈问题
- **理论连接数**: 10 个任务 × 25 个连接 = 250 个并发连接
- **系统限制**: aria2c 全局连接数限制通常 100-200，系统文件描述符默认 1024
- **内存压力**: 10 个并发任务需要 2-5GB 内存，接近 4GB 限制

### 建议解决方案

#### 立即检查项
1. **检查 aria2c 状态**: `docker logs aria2-service`
2. **检查连接数**: `netstat -an | grep :6800 | wc -l`  
3. **检查资源使用**: `docker stats aria2-service`
4. **验证 RPC 连接**: `curl -X POST http://localhost:6800/jsonrpc`

#### 配置统一化建议
1. **统一连接数配置**: 将所有环境的 `max-connection-per-server` 设为 16
2. **调整并发策略**: 考虑将 10 个并发任务改为 5 个，减少资源竞争
3. **增加系统限制**: 提高文件描述符限制 (`ulimit -n 4096`)
4. **监控优化**: 添加 aria2c 连接数和队列长度监控

#### 性能优化建议
- **分批处理**: 将 10 个并发改为 2 批 × 5 个任务
- **连接数优化**: 每个任务使用 10-15 个连接而非 25 个
- **资源预留**: 考虑将 aria2-service 内存预留提升到 6GB

## APK处理I/O冲突分析 (2024-12-19)

### 问题现象
通过系统监控发现严重的磁盘I/O瓶颈：
- **磁盘利用率**: 100.10% (完全饱和)
- **写入等待时间**: 3939.49ms (极高，正常应<50ms)
- **I/O等待时间**: 24.31% (严重，正常应<5%)
- **磁盘**: nvme0n1 (NVMe SSD)

### I/O冲突根因分析

#### 1. 下载阶段I/O冲突
- **并发写入流**: 10个Prefect任务 × 5个分片 = 50个并发写入流
- **分片写入**: 每个分片20MB，频繁的分片写入导致磁盘碎片化
- **写入竞争**: 50个并发写入流竞争磁盘写入带宽
- **临时文件管理**: 频繁创建和重命名临时文件

#### 2. 分析阶段I/O冲突 (关键问题)
- **APK完整读取**: `APK(apk_file_path)`完整读取50-200MB文件到内存
- **DEX解压I/O**: `apk.get_all_dex()`解压APK内DEX文件，产生大量临时I/O
- **随机读取模式**: 遍历DEX类定义和文件列表，产生大量随机读取
- **并发分析冲突**: 10个任务同时进行APK分析，1GB+并发读取
- **内存压力**: 10个任务×100MB平均 = 1GB内存占用，可能触发swap
- **读写竞争**: 分析的随机读取与下载的顺序写入严重冲突

#### 3. 删除阶段I/O冲突 (重要问题)
- **频繁删除**: 每个任务完成后立即`os.remove()`删除临时文件
- **元数据同步**: 文件删除触发文件系统元数据同步写入
- **删除时机冲突**: 删除操作与其他任务的下载/分析同时进行
- **磁盘碎片化**: 频繁创建删除导致磁盘碎片，影响后续I/O性能
- **I/O调度冲突**: 删除、读取、写入操作竞争磁盘I/O调度器

### 优化策略

#### 立即优化 (紧急)
1. **错峰处理策略**
   - 修改流程：先完成所有下载任务，再批量进行分析
   - 避免下载和分析同时进行的I/O竞争
   - 实现方式：在`apk_processing_pipeline`中分阶段执行

2. **降低分析并发度**
   - 将分析并发度从10个降至3-4个
   - 减少同时进行的APK读取和DEX解压操作
   - 配置：修改`max_concurrent`参数

3. **优化删除策略**
   - 不在分析完成后立即删除临时文件
   - 实现批量清理：收集所有临时文件路径，统一删除
   - 错峰删除：在所有任务完成后进行清理

4. **内存优化**
   - 增加容器内存限制到8GB，减少swap使用
   - 监控内存使用，避免APK分析触发swap

#### 中期优化 (1-2周)
1. **存储分离**
   - 将临时文件存储到不同磁盘或高速SSD
   - 分离下载目录和分析临时目录
   - 使用内存文件系统(tmpfs)进行APK分析

2. **分析优化**
   - 实现APK分析结果缓存，避免重复分析
   - 使用流式APK分析，避免完整加载到内存
   - 优化androguard库使用，减少临时I/O

3. **I/O调度优化**
   - 使用ionice设置I/O优先级
   - 实现任务队列，控制I/O密集操作的并发度

#### 长期优化 (1个月)
1. **架构重构**
   - 将APK分析独立为单独的服务
   - 使用消息队列解耦下载和分析
   - 实现分布式APK分析

2. **存储优化**
   - 使用高性能存储(NVMe RAID)
   - 实现APK文件的分层存储
   - 优化文件系统配置(ext4 → xfs)

### 性能提升预期

#### 立即优化效果
- **磁盘利用率**: 100% → 60-70% (错峰处理+降低并发)
- **写入等待时间**: 3939ms → 200-500ms (减少I/O竞争)
- **I/O等待时间**: 24.31% → 8-12% (优化删除策略)
- **整体性能提升**: 40-60%

#### 中期优化效果  
- **磁盘利用率**: 60-70% → 40-50% (存储分离)
- **写入等待时间**: 200-500ms → 50-100ms (I/O调度优化)
- **I/O等待时间**: 8-12% → 3-5% (分析优化)
- **整体性能提升**: 70-80%

#### 长期优化效果
- **磁盘利用率**: 40-50% → 20-30% (架构重构)
- **写入等待时间**: 50-100ms → 10-20ms (存储优化)
- **I/O等待时间**: 3-5% → 1-2% (分布式处理)
- **整体性能提升**: 90%+

### 监控指标

#### 关键I/O指标
```bash
# 实时监控命令
iostat -x 1 3
```

**目标值**:
- `%util` < 80% (磁盘利用率)
- `await` < 100ms (平均等待时间)  
- `w_await` < 50ms (写入等待时间)
- CPU `%iowait` < 10% (I/O等待时间)

#### APK处理性能指标
- **下载速度**: 目标 > 50MB/s per task
- **分析速度**: 目标 < 30s per APK
- **任务完成率**: 目标 > 95%
- **内存使用**: 目标 < 6GB per container

### 立即行动建议

#### 紧急措施 (今天实施)
1. **降低并发度**: 修改配置将`max_concurrent`从10改为4
2. **错峰处理**: 修改`apk_processing_pipeline`实现分阶段执行
3. **批量删除**: 修改删除逻辑，收集临时文件路径统一清理

#### 监控部署 (明天实施)  
1. **部署I/O监控**: 设置iostat定时监控和告警
2. **内存监控**: 监控容器内存使用和swap情况
3. **任务性能监控**: 记录每个阶段的耗时和成功率

#### 配置优化 (本周实施)
1. **容器资源**: 增加内存限制到8GB
2. **文件系统**: 检查并优化文件系统挂载参数
3. **I/O调度**: 配置I/O调度器为deadline或mq-deadline

## Aria2 服务双实例问题修复 (2025-01-XX)

### 问题发现
通过 `docker ps | grep aria2` 发现系统中运行了两个 aria2-service 实例：
- 新实例: f88203138e71 (20秒前启动)
- 旧实例: c5df74294b92 (2小时前启动)

### 根本原因
1. **缺乏健康检查**: Docker Swarm 无法准确判断 aria2 服务状态
2. **宽松重启策略**: `condition: any` 导致服务重复启动
3. **更新策略不当**: 缺乏 `stop-first` 策略，新旧实例并存

### 解决方案
**修改 docker-swarm-compose.yml 中的 aria2-service 配置**:

1. **添加健康检查**:
   ```yaml
   healthcheck:
     test: ["CMD-SHELL", "curl -s -X POST http://localhost:6800/jsonrpc ..."]
     interval: 30s
     timeout: 10s
     retries: 3
     start_period: 60s
   ```

2. **严格更新策略**:
   ```yaml
   update_config:
     parallelism: 1
     delay: 30s
     failure_action: rollback
     order: stop-first  # 确保先停止旧实例
   ```

3. **优化重启策略**:
   ```yaml
   restart_policy:
     condition: on-failure  # 只在失败时重启
     delay: 30s
     max_attempts: 3
     window: 120s
   ```

4. **增加资源限制**:
   ```yaml
   resources:
     limits:
       memory: 6G
       cpus: '2.0'
     reservations:
       memory: 4G
       cpus: '1.0'
   ```

### 部署验证步骤
1. **清理现有服务**: `docker stack rm tenyy-stack`
2. **等待完全清理**: 等待 30-60 秒
3. **重新部署**: `docker stack deploy -c docker-swarm-compose.yml tenyy-stack`
4. **验证单实例**: `docker ps | grep aria2` (应该只有一个实例)
5. **测试 RPC**: 验证 aria2 RPC 接口正常工作

### 预期效果
- **消除双实例冲突**: 确保只有一个 aria2 服务运行
- **提高服务稳定性**: 健康检查和严格更新策略
- **避免端口冲突**: 6800/6888 端口不再竞争
- **减少资源浪费**: 避免重复的内存和 CPU 占用

---

## 项目日志产生和存储分析

### 日志产生情况
当前项目确实会产生**大量日志**，主要来源包括：

#### 1. 应用层日志
- **爬虫任务日志**：每个爬虫流程的执行记录、错误信息、数据处理状态
- **下载任务日志**：APK 下载进度、失败重试、文件处理状态
- **数据库操作日志**：SQL 查询、事务处理、连接池状态
- **HTTP 请求日志**：API 调用、响应状态、重试机制

#### 2. Prefect 系统日志
- **Flow 执行日志**：工作流启动、完成、失败状态
- **Task 执行日志**：每个任务的详细执行记录
- **Worker 日志**：工作节点的状态、任务分配、资源使用
- **调度器日志**：任务调度、队列管理、并发控制

#### 3. 基础设施日志
- **Docker 容器日志**：容器启动、停止、健康检查
- **数据库日志**：PostgreSQL 查询日志、连接日志、错误日志
- **Aria2 日志**：下载任务状态、RPC 调用、文件操作

### 日志存储位置

#### 1. 应用日志存储
根据 `tenyy/src/common/logging/config.py` 配置：

**Docker 环境**（生产环境）：
- **主要输出**：JSON 格式到 `stdout`，被 Docker 日志驱动收集
- **错误日志**：`/app/logs/error.log`（容器内）
- **日志轮转**：100MB 轮转，保留 10 天，压缩存储

**本地开发环境**：
- **控制台输出**：彩色格式到 `stderr`
- **JSON 日志**：`logs/app.json`
- **标准日志**：`logs/app.log`
- **错误日志**：`logs/error.log`

#### 2. Prefect Server 日志存储
根据 `docker-swarm-compose.yml` 配置：

**Prefect Server 数据**：
- **数据库**：PostgreSQL (`prefect-db` 服务)
  - 连接字符串：`postgresql+asyncpg://prefect:password@prefect-db:5432/prefect_server`
  - 数据卷：`prefect_server_data:/root/.prefect`
- **API 日志**：通过 Docker 日志驱动收集
- **UI 访问日志**：HTTP 请求日志

**Prefect Worker 日志**：
- **执行日志**：通过 Docker 日志驱动收集
- **任务状态**：存储在 Prefect Server 数据库中

#### 3. 日志收集机制

**Docker 日志驱动**：
- 所有容器的 `stdout/stderr` 被 Docker 收集
- 默认使用 `json-file` 驱动
- 日志位置：`/var/lib/docker/containers/<container_id>/<container_id>-json.log`

**Prefect 日志集成**：
- 应用代码中使用 `get_run_logger()` 获取 Prefect 日志器
- 日志同时输出到：
  1. Prefect Server 数据库（用于 UI 显示）
  2. 容器标准输出（被 Docker 收集）
  3. 应用日志文件（错误日志）

### 日志量级估算

#### 高频日志源
1. **爬虫任务**：每个任务 ~1-5MB 日志
2. **下载任务**：每个 APK ~100KB-1MB 日志
3. **HTTP 请求**：每个请求 ~1-10KB 日志
4. **数据库操作**：每个操作 ~100B-1KB 日志

#### 日常日志量
- **单个爬虫流程**：~10-50MB
- **10 个并发下载**：~10-100MB
- **每日总量**：~500MB-2GB（取决于任务频率）

### 日志管理建议

#### 1. 立即优化
- **日志级别调整**：生产环境设置为 `WARNING` 或 `ERROR`
- **减少调试日志**：移除不必要的 `DEBUG` 和 `INFO` 日志
- **压缩存储**：启用日志压缩（已配置）

#### 2. 长期方案
- **日志聚合**：考虑使用 ELK Stack 或 Grafana Loki
- **日志分析**：设置日志监控和告警
- **存储优化**：使用 SSD 存储热日志，冷日志迁移到 HDD

### 版本控制建议
鉴于日志配置的重要性，建议：
1. **提交当前配置**：`git add . && git commit -m "docs: 添加日志分析和存储配置文档"`
2. **创建配置分支**：`git checkout -b feature/logging-optimization`
3. **跟踪配置变更**：所有日志配置修改都应版本控制

---

## 🚨 PostgreSQL 数据库损坏问题修复记录 (2025-01-27)

### 🔍 问题现象
- **错误信息**: "invalid primary checkpoint record", "could not locate a valid checkpoint record"
- **影响**: PostgreSQL 无法启动，数据库服务不可用
- **原因**: 数据库文件损坏，可能由于异常关闭、磁盘问题或系统崩溃导致

### 🛠️ 修复方案

#### 1. 诊断步骤
```bash
# 查看服务状态和日志
docker service ls
docker service logs tenyy-crawler-stack_app-db
docker service logs tenyy-crawler-stack_prefect-db
```

#### 2. 修复流程
1. **停止服务**: `docker stack rm tenyy-crawler-stack`
2. **备份数据**: 即使损坏也要备份到 `/mnt/ssd/tenyy/backup/`
3. **删除损坏文件**: 清空对应的数据库目录
4. **重新部署**: 使用 `make step3` 或手动部署
5. **验证服务**: 检查日志和健康状态

#### 3. 数据库路径
- **应用数据库**: `/mnt/ssd/tenyy/db`
- **Prefect数据库**: `/mnt/ssd/tenyy/prefect_db`

### ⚠️ 重要提醒
- **数据丢失**: 修复过程会导致数据库数据完全丢失
- **备份重要性**: 定期备份可以减少数据丢失风险
- **优雅关闭**: 避免强制终止数据库进程

### 🔄 预防措施
1. 定期数据库备份
2. 监控磁盘空间和健康状态
3. 使用优雅的服务停止方式
4. 考虑使用数据库集群提高可用性

---

## 📝 batch_processor.py 优化修改记录

### 🔧 修改内容

#### 1. 禁用超时清理机制
- **原因**: 避免任务重复执行和循环问题
- **修改**: 注释掉 `cleanup_stale_processing_tasks` 任务
- **影响**: 不再自动清理超时的 `processing` 状态任务

#### 2. 数据库驱动升级
- **从**: 同步的 `psycopg2` 
- **到**: 异步的 `SQLAlchemy` + `asyncpg`
- **优势**: 
  - 避免数据库操作阻塞
  - 提高并发性能
  - 与项目其他模块保持一致

#### 3. 具体代码变更

**导入模块更新**:
```python
# 移除
import psycopg2
from psycopg2.extras import RealDictCursor

# 新增
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, update, text
from tenyy.src.models.app_version import AppVersion
```

**配置更新**:
```python
# 从
from tenyy.src.download_extract.config import DATABASE_URL_SYNC

# 到
from tenyy.src.download_extract.config import DATABASE_URL
```

**get_pending_tasks 函数重构**:
- 使用异步 SQLAlchemy 会话
- 保持原有的原子操作逻辑（UPDATE...RETURNING）
- 自动管理数据库连接生命周期

### 🎯 预期效果

1. **性能提升**: 异步数据库操作避免阻塞
2. **稳定性增强**: 减少任务重复执行问题
3. **代码一致性**: 与 `apk_processor.py` 保持相同的数据库访问模式

### ⚠️ 注意事项

1. **部署后验证**: 确认异步数据库连接正常工作
2. **监控任务状态**: 观察是否还有任务卡在 `processing` 状态
3. **性能监控**: 对比修改前后的处理速度和资源使用

### 🔄 版本控制建议

**重要**: 这些修改涉及核心业务逻辑，建议进行版本控制：

1. **提交当前修改**:
   ```bash
   git add tenyy/src/download_extract/flows/batch_processor.py
   git commit -m "feat: 禁用超时清理机制并升级为异步数据库操作

   - 禁用 cleanup_stale_processing_tasks 避免任务重复执行
   - 将 psycopg2 替换为 SQLAlchemy + asyncpg 异步操作
   - 提高数据库操作性能和并发能力"
   ```

2. **创建功能分支** (可选):
   ```bash
   git checkout -b feature/async-batch-processor
   ```

3. **部署前备份**: 确保可以快速回滚到之前版本

---

# 生产环境网络配置不一致问题修复 (2024-12-19)

## 问题发现

在分析用户报告的生产环境 `tenyy-unified` 容器网络连接问题时，发现了**关键的配置不一致问题**：

### 网络名称不一致
在 `tenyy/config/production.py` 文件中存在两个不同的网络名称：

1. **第83行 NETWORK_CONFIG**:
   ```python
   NETWORK_CONFIG = {
       "docker_network": "tenyy-crawler-stack_tenyy-net",  # ❌ 错误的网络名称
   }
   ```

2. **第210行 DOCKER_NETWORK**:
   ```python
   DOCKER_NETWORK = "tenyy-stack_tenyy-net"  # ✅ 正确的网络名称
   ```

### 实际网络名称验证
根据用户成功的修复操作：
```bash
docker network connect tenyy-stack_tenyy-net 3d1a9b93b692
```
确认实际的 Docker Swarm 网络名称是 `tenyy-stack_tenyy-net`。

## 根本原因分析

1. **配置不一致**: 同一个配置文件中使用了两个不同的网络名称
2. **引用混乱**: 不同的代码模块可能引用了不同的网络配置
3. **部署时网络丢失**: Prefect deployment 可能使用了错误的网络名称，导致容器启动时没有连接到正确的网络

## 解决方案

### 立即修复
需要统一 `tenyy/config/production.py` 中的网络名称为 `tenyy-stack_tenyy-net`：

```python
# 修改第83行的 NETWORK_CONFIG
NETWORK_CONFIG = {
    "docker_network": "tenyy-stack_tenyy-net",  # 统一为正确的网络名称
    "network_mode": "tenyy-stack_tenyy-net",
}
```

### 验证步骤
1. 修复配置文件中的网络名称不一致
2. 重新生成 Prefect 配置：`python generate_prefect_config.py production`
3. 重新部署 Prefect deployments
4. 验证新启动的容器是否正确连接到网络

### 预防措施
1. **配置统一性检查**: 添加配置验证函数检查网络名称一致性
2. **文档更新**: 在配置文件中明确标注正确的网络名称
3. **部署前验证**: 在部署脚本中添加网络连接验证

## 影响范围

这个配置不一致问题可能影响：
- Prefect deployment 启动的所有容器
- 容器间的网络通信
- 数据库连接（`app-db` 主机名解析）
- Aria2 服务连接

## 版本控制建议

此次修复涉及关键的网络配置变更，建议：
1. 立即提交配置修复
2. 标记为重要的生产环境修复
3. 在部署前进行充分测试

---

# Prefect 配置生成工具分析

## generate_prefect_config.py 工具功能

### 核心功能
- **动态配置生成**: 根据环境参数（production/development）生成对应的 `prefect.yaml` 配置文件
- **统一网络配置**: 通过 `get_env_config()` 函数从统一配置中获取 `docker_network` 参数
- **多环境支持**: 支持生产环境和开发环境的不同配置

### 网络配置机制
```python
# 所有 deployment 的 job_variables 中都包含:
"networks": [env_config["docker_network"]]
```

### 环境配置映射
- **生产环境**: `docker_network = "tenyy-stack_tenyy-net"`
- **开发环境**: `docker_network = "tenyy-dev_tenyy-net"`

## 网络连接问题分析

### 问题现象
用户报告的 `tenyy-unified` 容器（ID: `3d1a9b93b692`）未连接到任何 Docker 网络，导致：
- 无法解析 `app-db` 主机名
- 数据库连接失败
- 任务失败循环重试
- 产生大量错误日志
- 引发高 iowait

### 根本原因分析
1. **配置正确性**: `generate_prefect_config.py` 生成的配置中明确包含网络配置
2. **执行时差异**: Prefect deployment 实际执行时可能存在网络配置丢失
3. **可能原因**:
   - Prefect Worker 版本兼容性问题
   - Docker Swarm 网络配置传递问题
   - 容器启动时序问题
   - 环境变量传递异常

### 解决方案
1. **立即修复**: 手动连接容器到正确网络
   ```bash
   docker network connect tenyy-stack_tenyy-net 3d1a9b93b692
   ```

2. **预防措施**:
   - 在 Prefect Worker 配置中添加网络检查
   - 增加容器启动后的网络验证
   - 考虑在容器内添加网络连接重试机制

### 监控建议
- 定期检查 Prefect 启动的容器网络状态
- 监控数据库连接失败率
- 设置网络连接异常告警

## 版本控制建议

建议对以下配置变更进行版本控制：
- `generate_prefect_config.py` 脚本修改
- 环境配置文件变更
- 网络配置策略调整
- Prefect deployment 参数优化

---

## Aria2 UI 镜像冲突修复 (2024-12-19)

### 问题发现
发现 `hurlenko/aria2-ariang` 镜像包含完整的 aria2 服务，导致与 `aria2-service` 容器产生端口和资源竞争，这是导致 Aria2 服务卡死的根本原因之一。

### 解决方案
将 `aria2-ui` 服务的镜像从 `hurlenko/aria2-ariang` 更换为 `p3terx/ariang`：

**修改内容**:
- **镜像**: `hurlenko/aria2-ariang` → `p3terx/ariang`
- **端口**: `8080:8080` → `6880:6880`
- **配置**: 移除环境变量配置，使用命令行参数
- **资源**: 降低内存限制 `2G → 512M`，CPU限制 `1 → 0.5`

**优势**:
1. **避免冲突**: `p3terx/ariang` 只包含 AriaNg Web UI，不包含 aria2 服务
2. **资源节省**: 纯 UI 镜像资源占用更少
3. **稳定性**: 消除两个 aria2 实例的竞争问题

**访问地址**: http://192.168.1.101:6880

### 部署验证
使用 Makefile 进行分阶段验证：
- `make step1`: 本地测试
- `make step2`: 本地容器测试  
- `make step3`: 生产环境部署
