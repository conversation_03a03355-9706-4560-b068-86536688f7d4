# Tenyy 简化构建系统 - 只保留核心功能
# 版本: v2.3 - x86优化版
# 镜像优化: 7.94GB → 1.07GB (减少86.5%)
# 架构支持: x86_64 (统一架构，优化构建速度)

# ============================================================================
# 🎨 颜色定义
# ============================================================================
BOLD := \033[1m
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
CYAN := \033[36m
NC := \033[0m

# ============================================================================
# ⚙️ 配置变量
# ============================================================================
ENV ?= local
DOCKER_IMAGE := tenyy-unified
DOCKER_TAG := latest

.DEFAULT_GOAL := help

# ============================================================================
# 🚀 核心工作流命令 (代码修改后的三个步骤)
# ============================================================================

.PHONY: help
help: ## 显示帮助信息
	@echo "$(BOLD)$(BLUE)Tenyy 开发部署系统$(NC)"
	@echo "$(GREEN)📊 镜像优化: 7.94GB → 1.07GB (减少86.5%)$(NC)"
	@echo ""
	@echo "$(YELLOW)🚀 核心工作流 (代码修改后的三个步骤):$(NC)"
	@echo ""
	@echo "  $(BOLD)1. 本地测试$(NC)"
	@echo "  make step1                       - 本地环境测试"
	@echo ""
	@echo "  $(BOLD)2. 容器测试$(NC)"
	@echo "  make step2                       - 本地容器环境测试 (智能缓存)"
	@echo "  make step2-fast                  - 本地容器环境测试 (快速模式)"
	@echo ""
	@echo "  $(BOLD)3. 生产部署$(NC)"
	@echo "  make step3                       - 生产环境部署 (智能缓存)"
	@echo "  make step3-fast                  - 生产环境部署 (快速模式)"
	@echo ""
	@echo "$(CYAN)💡 快速发布流程:$(NC)"
	@echo "  make step1 && make step2 && make step3"
	@echo "$(CYAN)⚡ 超快发布流程 (频繁开发):$(NC)"
	@echo "  make step1 && make step2-fast && make step3-fast"
	@echo ""
	@echo "$(YELLOW)🔧 辅助命令:$(NC)"
	@echo "  make setup                       - 首次环境设置"
	@echo "  make info ENV=local              - 查看配置信息"
	@echo "  make clean                       - 清理缓存"
	@echo "  make build-fast ENV=xxx          - 快速构建 (跳过缓存检查)"
	@echo ""
	@echo "$(YELLOW)🌍 环境参数说明:$(NC)"
	@echo "  ENV=local                        - 本地开发环境"
	@echo "    • 数据库: localhost:5434       - 本地PostgreSQL"
	@echo "    • Prefect: localhost:4200      - 本地Prefect服务"
	@echo "    • 架构: x86_64 (统一架构)      - 本地Registry"
	@echo "    • 用途: 快速测试、调试代码      - 无需Docker"
	@echo ""
	@echo "  ENV=prefect_local_container      - 本地容器环境"
	@echo "    • 数据库: localhost:5434       - 本地PostgreSQL"
	@echo "    • Prefect: prefect-server:4200 - 容器内Prefect"
	@echo "    • 架构: x86_64 (统一架构)      - 本地Registry"
	@echo "    • 用途: 容器环境验证           - 模拟生产环境"
	@echo ""
	@echo "  ENV=production                   - 生产环境"
	@echo "    • 数据库: 生产数据库           - 生产PostgreSQL"
	@echo "    • Prefect: 生产Prefect服务     - 生产集群"
	@echo "    • 架构: x86_64 (统一架构)      - 生产Registry"
	@echo "    • 用途: 正式部署               - 真实生产环境"
	@echo ""
	@echo "$(CYAN)💡 使用流程:$(NC)"
	@echo "  $(GREEN)# 首次使用$(NC)"
	@echo "  make setup"
	@echo ""
	@echo "  $(GREEN)# 开发流程 (渐进式测试)$(NC)"
	@echo "  1. make local      # 本地快速测试 (5秒)"
	@echo "  2. make container  # 容器环境验证 (1-2分钟)"
	@echo "  3. make production # 生产环境部署 (1-2分钟)"
	@echo ""
	@echo "$(CYAN)🎯 环境切换示例:$(NC)"
	@echo "  make info ENV=local              # 查看本地环境配置"
	@echo "  make info ENV=prefect_local_container # 查看容器环境配置"
	@echo "  make info ENV=production         # 查看生产环境配置"

# ============================================================================
# 🚀 三步发布流程 (简化版)
# ============================================================================

.PHONY: step1
step1: ## 第1步: 本地测试
	@echo "$(BLUE)🏠 第1步: 本地环境测试$(NC)"
	@$(MAKE) local
	@echo "$(CYAN)==== prefect.yaml for local ====$(NC)"
	@cat prefect.yaml
	@echo "$(CYAN)==============================$(NC)"

.PHONY: step2
step2: ## 第2步: 容器测试
	@echo "$(BLUE)🐳 第2步: 本地容器环境测试$(NC)"
	@$(MAKE) container
	@echo "$(CYAN)==== prefect.yaml for container ====$(NC)"
	@cat prefect.yaml
	@echo "$(CYAN)==================================$(NC)"

.PHONY: step3
step3: ## 第3步: 生产部署
	@echo "$(BLUE)🏭 第3步: 生产环境部署$(NC)"
	@$(MAKE) production
	@echo "$(CYAN)==== prefect.yaml for production ====$(NC)"
	@cat prefect.yaml
	@echo "$(CYAN)===================================$(NC)"

.PHONY: step2-fast
step2-fast: ## 第2步: 容器测试 (快速模式)
	@echo "$(BLUE)🐳 第2步: 本地容器环境测试 (快速模式)$(NC)"
	@$(MAKE) container-fast
	@echo "$(CYAN)==== prefect.yaml for container ====$(NC)"
	@cat prefect.yaml
	@echo "$(CYAN)==================================$(NC)"

.PHONY: step3-fast
step3-fast: ## 第3步: 生产部署 (快速模式)
	@echo "$(BLUE)🏭 第3步: 生产环境部署 (快速模式)$(NC)"
	@$(MAKE) production-fast
	@echo "$(CYAN)==== prefect.yaml for production ====$(NC)"
	@cat prefect.yaml
	@echo "$(CYAN)===================================$(NC)"

# ============================================================================
# 🚀 原始核心工作流命令
# ============================================================================

.PHONY: local
local: ## 1. 本地环境测试 (提供测试命令)
	@echo "$(BLUE)🏠 本地环境测试 [ENV=local]$(NC)"
	@echo "$(YELLOW)⚡ 验证配置并提供测试命令$(NC)"
	@TENYY_ENV=local python -c "from tenyy.config import print_config_summary; print_config_summary()"
	@echo "$(GREEN)✅ 本地环境配置正常$(NC)"
	@echo ""
	@echo "$(CYAN)🧪 小规模测试命令 (复制到终端运行):$(NC)"
	@echo ""
	@echo "$(YELLOW)1. 测试爬虫Flow (小规模):$(NC)"
	@echo "$(BOLD)TENYY_ENV=local python -c \"from tenyy.src.crawler.containerized_crawler import run_containerized_crawl; run_containerized_crawl('yinyongbao', app_type='app', max_pages=1, max_categories=1)\"$(NC)"
	@echo ""
	@echo "$(YELLOW)2. 测试旧版批次处理Flow (小规模):$(NC)"
	@echo "$(BOLD)TENYY_ENV=local python -c \"import asyncio; from tenyy.src.download_extract.flows.batch_processor import batch_apk_processing_flow; asyncio.run(batch_apk_processing_flow(batch_size=3, max_concurrent=2))\"$(NC)"
	@echo ""
	@echo "$(YELLOW)3. 🚀 测试新版流水线Flow (小规模):$(NC)"
	@echo "$(BOLD)TENYY_ENV=local python -c \"import asyncio; from tenyy.src.download_extract.flows.batch_processor import continuous_pipeline_apk_processing_flow; asyncio.run(continuous_pipeline_apk_processing_flow(max_concurrent=3, task_fetch_size=5, max_idle_time=30, check_interval=3))\"$(NC)"
	@echo ""
	@echo "$(YELLOW)4. 测试配置验证:$(NC)"
	@echo "$(BOLD)TENYY_ENV=local python -c \"from tenyy.config import validate_config; print('配置验证:', '✅ 通过' if validate_config() else '❌ 失败')\"$(NC)"
	@echo ""
	@echo "$(GREEN)💡 推荐测试顺序: 先运行命令4验证配置，再运行命令3测试新版流水线Flow$(NC)"
	@echo "$(CYAN)💡 查看Flow运行结果: http://localhost:4200/runs$(NC)"
	@echo ""
	@echo "$(BLUE)🎯 快捷测试命令:$(NC)"
	@echo "$(BOLD)make test-pipeline$(NC)                    # 直接测试新版流水线Flow"
	@echo "$(BOLD)make pipeline-help$(NC)                   # 查看流水线Flow详细帮助"

.PHONY: container  
container: config-validate ## 2. 本地容器环境测试
	@echo "$(BLUE)🐳 本地容器环境测试 [ENV=prefect_local_container]$(NC)"
	@echo "$(YELLOW)⚡ 构建+推送+部署 (1-2分钟完成)$(NC)"
	@$(MAKE) build ENV=prefect_local_container
	@$(MAKE) push ENV=prefect_local_container
	@$(MAKE) deploy ENV=prefect_local_container
	@echo "$(GREEN)🎉 容器环境部署完成！$(NC)"
	@echo "$(CYAN)💡 启动Worker: export DOCKER_HOST=unix:///Users/<USER>/.docker/run/docker.sock && prefect worker start --pool tenyy-unified-pool$(NC)"

.PHONY: production
production: config-validate ## 3. 生产环境部署
	@echo "$(BLUE)🏭 生产环境部署 [ENV=production]$(NC)"
	@echo "$(YELLOW)⚡ 构建+推送+部署到生产环境$(NC)"
	@$(MAKE) build ENV=production
	@$(MAKE) push ENV=production
	@$(MAKE) deploy ENV=production
	@echo "$(GREEN)🎉 生产环境部署完成！$(NC)"

.PHONY: container-fast
container-fast: config-validate ## 2. 本地容器环境测试 (快速模式)
	@echo "$(BLUE)🐳 本地容器环境测试 [ENV=prefect_local_container] - 快速模式$(NC)"
	@echo "$(YELLOW)⚡ 快速构建+推送+部署 (跳过缓存检查)$(NC)"
	@$(MAKE) build-fast ENV=prefect_local_container
	@$(MAKE) push ENV=prefect_local_container
	@$(MAKE) deploy ENV=prefect_local_container
	@echo "$(GREEN)🎉 容器环境快速部署完成！$(NC)"
	@echo "$(CYAN)💡 启动Worker: export DOCKER_HOST=unix:///Users/<USER>/.docker/run/docker.sock && prefect worker start --pool tenyy-unified-pool$(NC)"

.PHONY: production-fast
production-fast: config-validate ## 3. 生产环境部署 (快速模式)
	@echo "$(BLUE)🏭 生产环境部署 [ENV=production] - 快速模式$(NC)"
	@echo "$(YELLOW)⚡ 快速构建+推送+部署到生产环境$(NC)"
	@$(MAKE) build-fast ENV=production
	@$(MAKE) push ENV=production
	@$(MAKE) deploy ENV=production
	@echo "$(GREEN)🎉 生产环境快速部署完成！$(NC)"

# ============================================================================
# 🔧 辅助命令
# ============================================================================

.PHONY: setup
setup: ## 首次环境设置
	@echo "$(BLUE)🔧 首次环境设置$(NC)"
	@chmod +x setup_shared_storage.sh
	@./setup_shared_storage.sh
	@echo "$(GREEN)✅ 环境设置完成$(NC)"

.PHONY: info
info: ## 显示配置信息
	@echo "$(BLUE)🔧 配置信息 [ENV=$(ENV)]$(NC)"
	@TENYY_ENV=$(ENV) python -c "from tenyy.config import print_config_summary; print_config_summary()"

.PHONY: clean
clean: ## 清理缓存和临时文件
	@echo "$(BLUE)🧹 清理缓存和临时文件$(NC)"
	@rm -f .last-*-hash
	@rm -f .last-src-hash
	@docker system prune -f
	@echo "$(GREEN)✅ 清理完成$(NC)"

.PHONY: test-download
test-download: ## 测试下载分析模块 (包含artifact)
	@echo "$(BLUE)🧪 测试下载分析模块$(NC)"
	@echo "$(YELLOW)⚡ 测试批量处理流程和artifact生成$(NC)"
	@TENYY_ENV=local python -c "import asyncio; from tenyy.src.download_extract.flows.batch_processor import batch_apk_processing_flow; asyncio.run(batch_apk_processing_flow(batch_size=3, max_concurrent=2))"
	@echo "$(GREEN)✅ 下载分析模块测试完成$(NC)"
	@echo "$(CYAN)💡 查看Prefect UI中的Artifact报告: http://localhost:4200$(NC)"

.PHONY: test-artifact
test-artifact: ## 测试artifact生成功能 (已验证通过)
	@echo "$(BLUE)🎨 Artifact功能已验证通过$(NC)"
	@echo "$(GREEN)✅ 下载分析模块已成功集成爬虫模块的artifact功能$(NC)"
	@echo "$(YELLOW)📊 支持的报告类型:$(NC)"
	@echo "  • 批量处理报告 (batch)"
	@echo "  • 全量处理报告 (full)"
	@echo "  • 智能状态评估 (🎉/⚠️/❌)"
	@echo "  • 详细统计数据和性能指标"
	@echo "$(CYAN)💡 查看实际报告: http://localhost:4200/artifacts$(NC)"

# ============================================================================
# 🏗️ 内部构建命令 (由核心工作流调用)
# ============================================================================

.PHONY: config-validate
config-validate: ## 验证配置完整性
	@echo "$(BLUE)✅ 验证配置 [ENV=$(ENV)]...$(NC)"
	@TENYY_ENV=$(ENV) python -c "from tenyy.config import validate_config; exit(0 if validate_config() else 1)"
	@echo "$(GREEN)✅ 配置验证通过$(NC)"

.PHONY: build
build: config-validate generate-prefect-config ## 构建Docker镜像 (智能缓存优化)
	@echo "$(BLUE)🏗️ 构建镜像 [ENV=$(ENV)] - 智能缓存优化$(NC)"
	@# 检查requirements.txt变更
	@if [ -f ".last-requirements-hash" ]; then \
		LAST_REQ_HASH=$$(cat .last-requirements-hash); \
		CURRENT_REQ_HASH=$$(md5 -q requirements.txt 2>/dev/null || md5sum requirements.txt | cut -d' ' -f1); \
		if [ "$$LAST_REQ_HASH" = "$$CURRENT_REQ_HASH" ]; then \
			echo "$(GREEN)✅ requirements.txt 未变更 - 使用缓存$(NC)"; \
		else \
			echo "$(YELLOW)📦 requirements.txt 已变更 - 重新安装依赖$(NC)"; \
		fi; \
	else \
		echo "$(YELLOW)📦 首次构建$(NC)"; \
	fi
	@# 检查源代码变更
	@CURRENT_SRC_HASH=$$(find tenyy/src -type f -name "*.py" -exec md5 -q {} \; 2>/dev/null | sort | md5 -q || find tenyy/src -type f -name "*.py" -exec md5sum {} \; | sort | md5sum | cut -d' ' -f1); \
	if [ -f ".last-src-hash" ]; then \
		LAST_SRC_HASH=$$(cat .last-src-hash); \
		if [ "$$LAST_SRC_HASH" = "$$CURRENT_SRC_HASH" ]; then \
			echo "$(GREEN)✅ 源代码未变更 - 最大化缓存利用$(NC)"; \
		else \
			echo "$(YELLOW)🔄 源代码已变更 - 重新构建应用层$(NC)"; \
		fi; \
	else \
		echo "$(YELLOW)📝 首次记录源代码哈希$(NC)"; \
	fi
	@echo "$(YELLOW)🏗️ 构建x86_64镜像 (优化缓存策略)$(NC)"
	@docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		--build-arg ENV_TYPE=$(ENV) \
		--build-arg BUILDKIT_INLINE_CACHE=1 \
		-f tenyy/container/Dockerfile .
	@# 保存哈希值用于下次比较
	@md5 -q requirements.txt 2>/dev/null > .last-requirements-hash || md5sum requirements.txt | cut -d' ' -f1 > .last-requirements-hash
	@echo "$$CURRENT_SRC_HASH" > .last-src-hash
	@echo "$(GREEN)✅ 镜像构建完成$(NC)"

.PHONY: build-fast
build-fast: config-validate generate-prefect-config ## 快速构建 (跳过缓存检查，适用于频繁开发)
	@echo "$(BLUE)🚀 快速构建镜像 [ENV=$(ENV)] - 开发模式$(NC)"
	@echo "$(YELLOW)⚡ 跳过缓存检查，直接构建...$(NC)"
	@docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		--build-arg ENV_TYPE=$(ENV) \
		--build-arg BUILDKIT_INLINE_CACHE=1 \
		-f tenyy/container/Dockerfile .
	@echo "$(GREEN)✅ 快速构建完成$(NC)"

.PHONY: push
push: ## 推送镜像 (统一x86_64架构)
	@echo "$(BLUE)📤 推送镜像 [ENV=$(ENV)] - x86_64架构$(NC)"
	@echo "$(YELLOW)�️ 标记并推送镜像...$(NC)"
	@if TENYY_ENV=$(ENV) python -c "from tenyy.config import get_docker_image; import subprocess; result1 = subprocess.run(['docker', 'tag', '$(DOCKER_IMAGE):$(DOCKER_TAG)', get_docker_image()]); result2 = subprocess.run(['docker', 'push', get_docker_image()]); exit(max(result1.returncode, result2.returncode))"; then \
		echo "$(GREEN)✅ 镜像推送完成$(NC)"; \
	else \
		echo "$(RED)❌ 镜像推送失败$(NC)"; \
		exit 1; \
	fi



.PHONY: generate-prefect-config
generate-prefect-config: ## 生成Prefect配置文件
	@echo "$(BLUE)🔧 生成Prefect配置 [ENV=$(ENV)]$(NC)"
	@python generate_prefect_config.py $(ENV)
	@echo "$(GREEN)✅ Prefect配置生成完成$(NC)"

.PHONY: deploy
deploy: config-validate generate-prefect-config ## 部署到Prefect
	@echo "$(BLUE)🚀 部署到Prefect [ENV=$(ENV)]$(NC)"
	@PREFECT_API_URL=$$(python get_prefect_api_url.py $(ENV) 2>/dev/null || echo -n "http://localhost:4200/api"); \
	echo "$(YELLOW)📡 部署到Prefect服务器: $$PREFECT_API_URL$(NC)"; \
	echo "$(YELLOW)⚠️ 自动回答交互式提示...$(NC)"; \
	(echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n"; echo "n") | \
	TENYY_ENV=$(ENV) PREFECT_API_URL=$$PREFECT_API_URL prefect deploy --all || \
	(echo "$(YELLOW)⚠️ 重试部署...$(NC)" && \
	 yes "n" | head -30 | TENYY_ENV=$(ENV) PREFECT_API_URL=$$PREFECT_API_URL prefect deploy --all)
	@echo "$(GREEN)✅ 部署完成$(NC)"

# ============================================================================
# 🚀 流水线Flow测试命令
# ============================================================================

.PHONY: test-pipeline
test-pipeline: ## 测试流水线APK处理Flow (本地)
	@echo "$(BLUE)🧪 测试流水线APK处理Flow [ENV=local]$(NC)"
	@TENYY_ENV=local python -c "import asyncio; from tenyy.src.download_extract.flows.batch_processor import continuous_pipeline_apk_processing_flow; asyncio.run(continuous_pipeline_apk_processing_flow(max_concurrent=5, task_fetch_size=10, max_idle_time=30, check_interval=3))"

.PHONY: test-pipeline-container
test-pipeline-container: ## 测试流水线APK处理Flow (本地容器)
	@echo "$(BLUE)🧪 测试流水线APK处理Flow [ENV=prefect_local_container]$(NC)"
	@TENYY_ENV=prefect_local_container python -c "import asyncio; from tenyy.src.download_extract.flows.batch_processor import continuous_pipeline_apk_processing_flow; asyncio.run(continuous_pipeline_apk_processing_flow(max_concurrent=8, task_fetch_size=15, max_idle_time=60, check_interval=5))"

.PHONY: run-pipeline
run-pipeline: ## 运行流水线APK处理Flow (通过Prefect)
	@echo "$(BLUE)🚀 运行流水线APK处理Flow [ENV=$(ENV)]$(NC)"
	@PREFECT_API_URL=$$(python get_prefect_api_url.py $(ENV) 2>/dev/null || echo -n "http://localhost:4200/api"); \
	echo "$(YELLOW)📡 连接到Prefect服务器: $$PREFECT_API_URL$(NC)"; \
	TENYY_ENV=$(ENV) PREFECT_API_URL=$$PREFECT_API_URL prefect deployment run "真正的流水线APK处理流程/pipeline-apk-processing"

.PHONY: run-pipeline-custom
run-pipeline-custom: ## 运行流水线APK处理Flow (自定义参数)
	@echo "$(BLUE)🚀 运行流水线APK处理Flow (自定义参数) [ENV=$(ENV)]$(NC)"
	@echo "$(YELLOW)📝 参数说明:$(NC)"
	@echo "  max_concurrent: 并发工作者数量 (建议: 5-20)"
	@echo "  task_fetch_size: 每次获取任务数 (建议: 10-50)"
	@echo "  max_idle_time: 空闲超时时间/秒 (建议: 30-300)"
	@echo "  check_interval: 检查新任务间隔/秒 (建议: 3-10)"
	@echo ""
	@read -p "max_concurrent (默认10): " concurrent; \
	read -p "task_fetch_size (默认20): " fetch_size; \
	read -p "max_idle_time (默认60): " idle_time; \
	read -p "check_interval (默认5): " interval; \
	concurrent=$${concurrent:-10}; \
	fetch_size=$${fetch_size:-20}; \
	idle_time=$${idle_time:-60}; \
	interval=$${interval:-5}; \
	echo "$(YELLOW)🚀 启动参数: concurrent=$$concurrent, fetch_size=$$fetch_size, idle_time=$$idle_time, interval=$$interval$(NC)"; \
	PREFECT_API_URL=$$(python get_prefect_api_url.py $(ENV) 2>/dev/null || echo -n "http://localhost:4200/api"); \
	TENYY_ENV=$(ENV) PREFECT_API_URL=$$PREFECT_API_URL prefect deployment run "真正的流水线APK处理流程/pipeline-apk-processing" \
		--param max_concurrent=$$concurrent \
		--param task_fetch_size=$$fetch_size \
		--param max_idle_time=$$idle_time \
		--param check_interval=$$interval

.PHONY: pipeline-help
pipeline-help: ## 显示流水线Flow使用帮助
	@echo "$(BLUE)🚀 流水线APK处理Flow 使用指南$(NC)"
	@echo ""
	@echo "$(YELLOW)📋 可用命令:$(NC)"
	@echo "  make test-pipeline              - 本地测试流水线Flow"
	@echo "  make test-pipeline-container    - 本地容器测试流水线Flow"
	@echo "  make run-pipeline ENV=local     - 通过Prefect运行流水线Flow"
	@echo "  make run-pipeline-custom        - 自定义参数运行流水线Flow"
	@echo ""
	@echo "$(YELLOW)⚙️ 参数说明:$(NC)"
	@echo "  max_concurrent   - 并发工作者数量"
	@echo "    • 建议值: 5-20"
	@echo "    • 太少: 资源浪费，处理慢"
	@echo "    • 太多: 可能超载，影响稳定性"
	@echo ""
	@echo "  task_fetch_size  - 每次从数据库获取的任务数"
	@echo "    • 建议值: 10-50"
	@echo "    • 太少: 数据库查询频繁"
	@echo "    • 太多: 内存占用大，不够灵活"
	@echo ""
	@echo "  max_idle_time    - 空闲多久后停止（秒）"
	@echo "    • 建议值: 30-300"
	@echo "    • 太短: 可能过早停止"
	@echo "    • 太长: 资源占用时间长"
	@echo ""
	@echo "  check_interval   - 检查新任务的间隔（秒）"
	@echo "    • 建议值: 3-10"
	@echo "    • 太短: 数据库压力大"
	@echo "    • 太长: 发现新任务延迟"
	@echo ""
	@echo "$(YELLOW)🎯 推荐配置:$(NC)"
	@echo "  本地开发:     max_concurrent=5,  task_fetch_size=10, max_idle_time=30,  check_interval=3"
	@echo "  本地容器:     max_concurrent=8,  task_fetch_size=15, max_idle_time=60,  check_interval=5"
	@echo "  生产环境:     max_concurrent=15, task_fetch_size=30, max_idle_time=120, check_interval=5"
