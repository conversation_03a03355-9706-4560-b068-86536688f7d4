#!/bin/bash

# Tenyy 数据库恢复脚本
# 用于从备份文件恢复 PostgreSQL 数据库

set -e  # 遇到错误时退出

# 数据库连接信息（从环境变量获取或使用默认值）
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"tenyy_app"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"zhangdi168"}

# 导出密码环境变量供 pg_dump 使用
export PGPASSWORD="${DB_PASSWORD}"

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 清理现有数据函数
cleanup_existing_data() {
    log "清理现有数据..."
    
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" <<-EOF
        DO \$\$ 
        DECLARE 
            r RECORD; 
        BEGIN 
            -- 禁用所有触发器
            SET session_replication_role = replica;
            
            -- 清空所有表的数据
            FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP 
                EXECUTE 'TRUNCATE TABLE ' || quote_ident(r.tablename) || ' CASCADE'; 
            END LOOP; 
            
            -- 重新启用所有触发器
            SET session_replication_role = DEFAULT;
        END \$\$;
	EOF
	
    if [ $? -eq 0 ]; then
        log "现有数据清理完成"
    else
        log "清理现有数据失败"
        exit 1
    fi
}

# 恢复全量备份函数
restore_full_backup() {
    local backup_file=$1
    local clean_data=$2
    
    if [ ! -f "${backup_file}" ]; then
        log "错误: 备份文件不存在: ${backup_file}"
        exit 1
    fi
    
    # 如果需要清理现有数据
    if [ "${clean_data}" = "true" ]; then
        cleanup_existing_data
    fi
    
    log "开始恢复全量备份: ${backup_file}"
    
    # 如果是压缩文件，先解压并恢复；否则直接恢复
    if [[ "${backup_file}" == *.gz ]]; then
        log "检测到压缩文件，正在解压并恢复..."
        gunzip -c "${backup_file}" | psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}"
    else
        log "正在恢复未压缩的备份文件..."
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -f "${backup_file}"
    fi
    
    if [ $? -eq 0 ]; then
        log "全量备份恢复成功"
    else
        log "全量备份恢复失败"
        exit 1
    fi
}

# 恢复增量备份函数
restore_incremental_backup() {
    local backup_file=$1
    
    if [ ! -f "${backup_file}" ]; then
        log "错误: 备份文件不存在: ${backup_file}"
        exit 1
    fi
    
    log "开始恢复增量备份: ${backup_file}"
    
    # 增量备份只包含数据部分，需要确保数据库结构已存在
    if [[ "${backup_file}" == *.gz ]]; then
        log "检测到压缩文件，正在解压并恢复..."
        gunzip -c "${backup_file}" | psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}"
    else
        log "正在恢复未压缩的备份文件..."
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -f "${backup_file}"
    fi
    
    if [ $? -eq 0 ]; then
        log "增量备份恢复成功"
    else
        log "增量备份恢复失败"
        exit 1
    fi
}

# 显示用法
show_usage() {
    echo "用法: $0 <full|incremental> <备份文件路径> [clean]"
    echo "示例:"
    echo "  $0 full /mnt/ssd/tenyy/db_backups/full/full_backup_20250731_030000.sql.gz"
    echo "  $0 full /mnt/ssd/tenyy/db_backups/full/full_backup_20250731_030000.sql.gz clean"
    echo "  $0 incremental /mnt/ssd/tenyy/db_backups/incremental/incremental_backup_20250731_091000.sql.gz"
    echo ""
    echo "参数说明:"
    echo "  full         - 恢复全量备份"
    echo "  incremental  - 恢复增量备份"
    echo "  clean        - (可选) 恢复前清理现有数据"
    echo ""
    echo "注意: 恢复前请确保目标数据库已存在"
}

# 主程序逻辑
main() {
    if [ $# -lt 2 ] || [ $# -gt 3 ]; then
        show_usage
        exit 1
    fi
    
    local backup_type=$1
    local backup_file=$2
    local clean_data="false"
    
    if [ "$3" = "clean" ]; then
        clean_data="true"
    fi
    
    case "${backup_type}" in
        "full")
            restore_full_backup "${backup_file}" "${clean_data}"
            ;;
        "incremental")
            restore_incremental_backup "${backup_file}"
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"