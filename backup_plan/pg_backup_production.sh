#!/bin/bash

# PostgreSQL 数据库生产环境备份脚本
# 支持全量备份和基于WAL的增量备份
# 全量备份：每天执行
# 增量备份：基于WAL日志

set -e  # 遇到错误时退出

# 备份基础目录
BACKUP_BASE_DIR="/mnt/ssd/tenyy/db_backups"
FULL_BACKUP_DIR="${BACKUP_BASE_DIR}/full"
INCREMENTAL_BACKUP_DIR="${BACKUP_BASE_DIR}/incremental"
WAL_BACKUP_DIR="${BACKUP_BASE_DIR}/wal"

# 创建备份目录
mkdir -p "${FULL_BACKUP_DIR}"
mkdir -p "${INCREMENTAL_BACKUP_DIR}"
mkdir -p "${WAL_BACKUP_DIR}"

# 获取当前时间
CURRENT_DATE=$(date +"%Y%m%d")
CURRENT_TIME=$(date +"%Y%m%d_%H%M%S")

# 数据库连接信息（从环境变量获取或使用默认值）
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"tenyy_app"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"zhangdi168"}

# 导出密码环境变量供 pg_dump 使用
export PGPASSWORD="${DB_PASSWORD}"

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 全量备份函数（基础备份）
base_backup() {
    log "开始执行生产环境全量备份（基础备份）"
    
    local backup_dir="${FULL_BACKUP_DIR}/base_${CURRENT_TIME}"
    
    # 创建备份目录
    mkdir -p "${backup_dir}"
    
    # 执行基础备份
    if pg_basebackup -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -D "${backup_dir}" -P -v; then
        log "生产环境全量备份成功: ${backup_dir}"
        
        # 创建压缩包
        tar -czf "${backup_dir}.tar.gz" -C "${FULL_BACKUP_DIR}" "base_${CURRENT_TIME}"
        
        # 删除原始目录以节省空间
        rm -rf "${backup_dir}"
        
        log "全量备份压缩完成: ${backup_dir}.tar.gz"
        
        # 删除7天前的全量备份
        find "${FULL_BACKUP_DIR}" -name "base_*.tar.gz" -mtime +7 -delete
        log "已清理7天前的全量备份文件"
    else
        log "生产环境全量备份失败"
        exit 1
    fi
}

# WAL日志备份函数
wal_backup() {
    log "开始执行WAL日志备份"
    
    # 这里需要数据库启用WAL归档
    # 在实际使用中，需要配置postgresql.conf中的archive_command
    
    # 示例：手动触发WAL切换
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT pg_switch_wal();"
    
    log "WAL日志备份完成"
}

# 基于时间点的逻辑增量备份函数
incremental_backup_by_time() {
    log "开始执行基于时间点的增量备份"
    
    local backup_file="${INCREMENTAL_BACKUP_DIR}/incremental_${CURRENT_TIME}.sql"
    
    # 计算一小时前的时间
    ONE_HOUR_AGO=$(date -d '1 hour ago' +"%Y-%m-%d %H:%M:%S")
    
    # 为每个关键表导出最近更新的数据
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=app \
           --data-only \
           --column-inserts \
           --where="updated_at >= '${ONE_HOUR_AGO}'" \
           > "${backup_file}.tmp"
    
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=store_app \
           --data-only \
           --column-inserts \
           --where="updated_at >= '${ONE_HOUR_AGO}'" \
           >> "${backup_file}.tmp"
    
    pg_dump -h "${DB_HOST}" \
           -p "${DB_PORT}" \
           -U "${DB_USER}" \
           -d "${DB_NAME}" \
           --table=app_version \
           --data-only \
           --column-inserts \
           --where="last_updated >= '${ONE_HOUR_AGO}' OR analyzed_at >= '${ONE_HOUR_AGO}' OR last_attempt >= '${ONE_HOUR_AGO}'" \
           >> "${backup_file}.tmp"
    
    # 添加标识信息
    echo "-- 增量备份时间范围: ${ONE_HOUR_AGO} 到 $(date +"%Y-%m-%d %H:%M:%S")" > "${backup_file}"
    cat "${backup_file}.tmp" >> "${backup_file}"
    rm "${backup_file}.tmp"
    
    if [ $? -eq 0 ]; then
        log "基于时间点的增量备份成功: ${backup_file}"
        
        # 压缩备份文件
        gzip "${backup_file}"
        log "增量备份文件已压缩: ${backup_file}.gz"
        
        # 删除3天前的增量备份
        find "${INCREMENTAL_BACKUP_DIR}" -name "incremental_*.sql.gz" -mtime +3 -delete
        log "已清理3天前的增量备份文件"
    else
        log "基于时间点的增量备份失败"
        exit 1
    fi
}

# 显示用法
show_usage() {
    echo "用法: $0 <base|incremental|wal>"
    echo "示例:"
    echo "  $0 base         - 执行基础备份（全量备份）"
    echo "  $0 incremental  - 执行基于时间点的增量备份"
    echo "  $0 wal          - 执行WAL日志备份"
}

# 根据参数决定执行哪种备份
case "${1:-base}" in
    "base")
        base_backup
        ;;
    "incremental")
        incremental_backup_by_time
        ;;
    "wal")
        wal_backup
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

# 取消密码环境变量
unset PGPASSWORD

log "备份任务完成"