#!/bin/bash

# PostgreSQL 数据库备份脚本
# 支持全量备份和增量备份
# 全量备份：每天执行
# 增量备份：每3小时执行

set -e  # 遇到错误时退出

# 备份基础目录
BACKUP_BASE_DIR="/mnt/ssd/tenyy/db_backups"
FULL_BACKUP_DIR="${BACKUP_BASE_DIR}/full"
INCREMENTAL_BACKUP_DIR="${BACKUP_BASE_DIR}/incremental"

# 创建备份目录
mkdir -p "${FULL_BACKUP_DIR}"
mkdir -p "${INCREMENTAL_BACKUP_DIR}"

# 获取当前时间
CURRENT_DATE=$(date +"%Y%m%d")
CURRENT_TIME=$(date +"%Y%m%d_%H%M%S")

# 数据库连接信息（从环境变量获取或使用默认值）
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"tenyy_app"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"zhangdi168"}

# 导出密码环境变量供 pg_dump 使用
export PGPASSWORD="${DB_PASSWORD}"

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 全量备份函数
full_backup() {
    log "开始执行全量备份"
    
    local backup_file="${FULL_BACKUP_DIR}/full_backup_${CURRENT_TIME}.sql"
    
    # 执行全量备份，包含 --clean 和 --if-exists 选项以便更好地处理恢复
    if pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" \
        --clean \
        --if-exists \
        --create \
        > "${backup_file}"; then
        log "全量备份成功: ${backup_file}"
        
        # 压缩备份文件
        gzip "${backup_file}"
        log "全量备份文件已压缩: ${backup_file}.gz"
        
        # 删除7天前的全量备份
        find "${FULL_BACKUP_DIR}" -name "full_backup_*.sql.gz" -mtime +7 -delete
        log "已清理7天前的全量备份文件"
    else
        log "全量备份失败"
        exit 1
    fi
}

# 增量备份函数
incremental_backup() {
    log "开始执行增量备份"
    
    local backup_file="${INCREMENTAL_BACKUP_DIR}/incremental_backup_${CURRENT_TIME}.sql"
    
    # 获取最近一次备份后的更改
    # 使用 pg_dump 的 --section 选项进行增量备份模拟
    # 在实际生产环境中，可能需要使用其他方式实现真正的增量备份
    
    if pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" --section=data > "${backup_file}"; then
        log "增量备份成功: ${backup_file}"
        
        # 压缩备份文件
        gzip "${backup_file}"
        log "增量备份文件已压缩: ${backup_file}.gz"
        
        # 删除3天前的增量备份
        find "${INCREMENTAL_BACKUP_DIR}" -name "incremental_backup_*.sql.gz" -mtime +3 -delete
        log "已清理3天前的增量备份文件"
    else
        log "增量备份失败"
        exit 1
    fi
}

# 根据参数决定执行哪种备份
case "${1:-full}" in
    "full")
        full_backup
        ;;
    "incremental")
        incremental_backup
        ;;
    *)
        log "用法: $0 [full|incremental]"
        exit 1
        ;;
esac

# 取消密码环境变量
unset PGPASSWORD

log "备份任务完成"