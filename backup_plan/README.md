# PostgreSQL 数据库备份计划

该备份计划包含全量备份和增量备份两种方式，用于定期备份 PostgreSQL 数据库。

## 备份策略

1. **全量备份**：每天凌晨3点执行，保留最近7天
2. **增量备份**：每3小时执行一次（在每小时的第10分钟），保留最近3天

## 文件说明

- [pg_backup.sh](file:///home/<USER>/dev/tenyy-dind/backup_plan/pg_backup.sh) - 标准备份脚本
- [pg_backup_production.sh](file:///home/<USER>/dev/tenyy-dind/backup_plan/pg_backup_production.sh) - 生产环境备份脚本
- [restore_db.sh](file:///home/<USER>/dev/tenyy-dind/backup_plan/restore_db.sh) - 数据库恢复脚本
- [crontab.conf](file:///home/<USER>/dev/tenyy-dind/backup_plan/crontab.conf) - 定时任务配置
- [test_backup.sh](file:///home/<USER>/dev/tenyy-dind/backup_plan/test_backup.sh) - 测试脚本
- README.md - 说明文档

## 使用方法

### 手动执行备份

```bash
# 执行全量备份
./pg_backup.sh full

# 执行增量备份
./pg_backup.sh incremental
```

### 生产环境备份

```bash
# 执行基础备份（全量备份）
./pg_backup_production.sh base

# 执行基于时间点的增量备份
./pg_backup_production.sh incremental

# 执行WAL日志备份
./pg_backup_production.sh wal
```

### 设置定时任务

将 [crontab.conf](file:///home/<USER>/dev/tenyy-dind/backup_plan/crontab.conf) 中的内容添加到系统的 crontab 中：

```bash
crontab -e
# 然后将 crontab.conf 的内容粘贴到打开的文件中
```

或者直接导入：

```bash
crontab crontab.conf
```

### 恢复数据库

```bash
# 恢复全量备份
./restore_db.sh full /mnt/ssd/tenyy/db_backups/full/full_backup_20250731_030000.sql.gz

# 恢复全量备份并清理现有数据
./restore_db.sh full /mnt/ssd/tenyy/db_backups/full/full_backup_20250731_030000.sql.gz clean

# 恢复增量备份 (需要先恢复全量备份)
./restore_db.sh incremental /mnt/ssd/tenyy/db_backups/incremental/incremental_backup_20250731_091000.sql.gz
```

## 生产环境恢复策略

### 完整恢复流程

1. **备份当前生产数据**（预防性措施）：
```bash
./pg_backup.sh full
```

2. **恢复基础备份**：
```bash
./restore_db.sh full /mnt/ssd/tenyy/db_backups/full/base_20250731_030000.tar.gz clean
```

3. **按时间顺序恢复增量备份**：
```bash
./restore_db.sh incremental /mnt/ssd/tenyy/db_backups/incremental/incremental_20250731_061000.sql.gz
./restore_db.sh incremental /mnt/ssd/tenyy/db_backups/incremental/incremental_20250731_091000.sql.gz
```

### 点对点恢复流程

如果需要恢复到特定时间点：

1. 恢复最新的基础备份
2. 按时间顺序恢复该时间点之前的所有增量备份
3. （如果启用了WAL）应用WAL日志到特定时间点

### 配置环境变量

备份和恢复脚本需要以下环境变量：

- `DB_HOST`: 数据库主机地址，默认为 localhost
- `DB_PORT`: 数据库端口，默认为 5432
- `DB_NAME`: 数据库名称，默认为 tenyy_app
- `DB_USER`: 数据库用户名，默认为 admin
- `DB_PASSWORD`: 数据库密码，默认为 zhangdi168

可以通过以下方式设置环境变量：

```bash
export DB_HOST=your_host
export DB_PORT=your_port
export DB_NAME=your_database
export DB_USER=your_username
export DB_PASSWORD=your_password
```

## 备份存储

备份文件存储在以下目录中：

- 全量备份：`/mnt/ssd/tenyy/db_backups/full/`
- 增量备份：`/mnt/ssd/tenyy/db_backups/incremental/`
- WAL日志备份：`/mnt/ssd/tenyy/db_backups/wal/`

## 恢复策略

1. **完全恢复**：使用最近的全量备份文件恢复
2. **点对点恢复**：先恢复最近全量备份，再按时间顺序应用增量备份

## 错误处理

### 重复键值错误

如果在恢复过程中遇到如下错误：
```
ERROR: duplicate key value violates unique constraint "app_pkey"
DETAIL: Key (id)=(com.kuaishou.nebula) already exists.
```

这表示您正在尝试恢复的数据与现有数据存在冲突。有两种解决方案：

1. **清理现有数据后恢复**（推荐）：
   ```bash
   ./restore_db.sh full /path/to/backup.sql.gz clean
   ```

2. **手动清理数据库**：
   ```bash
   psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "TRUNCATE app, app_version, store_app RESTART IDENTITY CASCADE;"
   ./restore_db.sh full /path/to/backup.sql.gz
   ```

## 测试

可以运行测试脚本来验证备份脚本的逻辑是否正确：

```bash
./test_backup.sh
```

注意：测试脚本仅验证脚本逻辑，不实际连接数据库。

## 注意事项

1. 确保 `/mnt/ssd/tenyy/db_backups/` 目录存在且具有适当的写入权限
2. 确保系统已安装 PostgreSQL 客户端工具（pg_dump 等）
3. 确保设置了正确的数据库连接参数
4. 建议定期检查备份任务的执行日志，确保备份正常工作
5. 恢复增量备份前必须先恢复全量备份
6. 恢复操作会覆盖数据库中的现有数据，请谨慎操作
7. 如果数据库中已存在数据，恢复前应先清理现有数据
8. 生产环境中执行恢复操作前，务必备份当前数据
9. 生产环境中建议使用 [pg_backup_production.sh](file:///home/<USER>/dev/tenyy-dind/backup_plan/pg_backup_production.sh) 脚本进行备份